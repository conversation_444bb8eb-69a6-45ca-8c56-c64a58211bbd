<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1\SecretVersion;

use UnexpectedValueException;

/**
 * The state of a
 * [SecretVersion][google.cloud.secretmanager.v1.SecretVersion], indicating if
 * it can be accessed.
 *
 * Protobuf type <code>google.cloud.secretmanager.v1.SecretVersion.State</code>
 */
class State
{
    /**
     * Not specified. This value is unused and invalid.
     *
     * Generated from protobuf enum <code>STATE_UNSPECIFIED = 0;</code>
     */
    const STATE_UNSPECIFIED = 0;
    /**
     * The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] may be
     * accessed.
     *
     * Generated from protobuf enum <code>ENABLED = 1;</code>
     */
    const ENABLED = 1;
    /**
     * The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] may not
     * be accessed, but the secret data is still available and can be placed
     * back into the
     * [ENABLED][google.cloud.secretmanager.v1.SecretVersion.State.ENABLED]
     * state.
     *
     * Generated from protobuf enum <code>DISABLED = 2;</code>
     */
    const DISABLED = 2;
    /**
     * The [SecretVersion][google.cloud.secretmanager.v1.SecretVersion] is
     * destroyed and the secret data is no longer stored. A version may not
     * leave this state once entered.
     *
     * Generated from protobuf enum <code>DESTROYED = 3;</code>
     */
    const DESTROYED = 3;

    private static $valueToName = [
        self::STATE_UNSPECIFIED => 'STATE_UNSPECIFIED',
        self::ENABLED => 'ENABLED',
        self::DISABLED => 'DISABLED',
        self::DESTROYED => 'DESTROYED',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(State::class, \Google\Cloud\SecretManager\V1\SecretVersion_State::class);

