<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

if (false) {
    /**
     * This class is deprecated. Use Google\Protobuf\Internal\FieldOptions\CType instead.
     * @deprecated
     */
    class FieldOptions_CType {}
}
class_exists(FieldOptions\CType::class);
@trigger_error('Google\Protobuf\Internal\FieldOptions_CType is deprecated and will be removed in the next major release. Use Google\Protobuf\Internal\FieldOptions\CType instead', E_USER_DEPRECATED);

