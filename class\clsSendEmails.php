<?php

declare(strict_types=1);

require ROOT_PATH . '/vendor/autoload.php';
require_once __DIR__ . '/../etc/cloud/secret_loader.php';


use \SendGrid\Mail\Mail;
use Mailgun\Mailgun;


class clsSendEmails
{
	var $schoolId = 0;
	var $SMTPHost = "";
	var $SMTPUserName = "";
	var $SMTPPassword  = "";
	var $SMTPPort  = "";
	var $SMTPFromName  = "";
	var $SMTPFromEmail  = "";
	var $EmailType  = "P";
	var $currenschoolDisplayname  = APPLICATION_NAME;
	var $currenschoolLogoImagePath = '';
	var $currenschoolURL = '';

	function __construct($schoolId)
	{
		$this->schoolId = $schoolId;

		//Get SMTP Details
		$objSMTPSettings = new clsSMTPSettings();
		$row = $objSMTPSettings->GetSMTPSettingDeatils($schoolId);
		unset($objSMTPSettings);
		if ($row) {

			$this->SMTPHost = stripslashes($row['SMTPHost']);
			$this->SMTPUserName = stripslashes($row['SMTPUserName']);
			$this->SMTPPassword  = stripslashes($row['SMTPPassword']);
			$this->SMTPPort  = stripslashes($row['SMTPPort']);
			$this->SMTPFromName  = stripslashes($row['SMTPFromName']);
			$this->SMTPFromEmail  = stripslashes($row['SMTPFromEmail']);
			$this->EmailType  = stripslashes($row['type']);
		}
		//------------------------------------------------------------------------
		//Get School Details
		//------------------------------------------------------------------------
		$this->currenschoolLogoImagePath = BASE_PATH . "/upload/schools/default-school.png";

		$objSchool = new clsSchool();
		$schooldetails =  $objSchool->GetSchoolDetails($schoolId);
		unset($objSchool);

		if ($schooldetails != '') {
			$this->currenschoolDisplayname = stripslashes($schooldetails['displayName']);

			$image_name =  stripslashes($schooldetails['logoSmallName']);
			$this->currenschoolLogoImagePath =  GetSchoolImagePath($schoolId, $image_name);

			$currenschoolIsActivateDomain = $schooldetails['isDomainActive'];
			$currenschoolDomain = stripslashes($schooldetails['domainName']);

			$dynamicOrgUrl = '';
			if ($currenschoolIsActivateDomain == 1) {
				$dynamicOrgUrl = 'http://' . $currenschoolDomain;
			} else {
				$dynamicLoginURL = BASE_PATH . '/school/' . $schooldetails['slug'];
			}
			$this->currenschoolURL = $dynamicLoginURL;
		}
		//------------------------------------------------------------------------

	}

	function __destruct() {}

	// function SendEmail($subject, $emailContent, $to, $attatchementPath = "")
	// {
	// 	$email = new Mail();
	// 	$email->setFrom("<EMAIL>", "Support");
	// 	$email->setSubject($subject);
	// 	$email->addTo($to);
	// 	$email->addContent(
	// 		"text/html",
	// 		$emailContent
	// 	);
	// 	$sendgrid = new \SendGrid(SENDGRID_API_KEY);
	// 	try {
	// 		$response = $sendgrid->send($email);
	// 		// print $response->statusCode() . "\n";
	// 		// print_r($response->headers());
	// 		// print $response->body() . "\n";
	// 		return $response->statusCode();
	// 	} catch (Exception $e) {
	// 		echo 'Caught exception: ' . $e->getMessage() . "\n";
	// 	}

	// 	// 		if($this->EmailType=='S')
	// 	// 		{

	// 	// 			//Send SMTP Email 
	// 	// 			//---------------------------------------
	// 	// 			$mailer = new PHPMailer();
	// 	// 			$mailer->Mailer = "smtp";
	// 	// 			$mailer->IsSMTP();
	// 	// 			$mailer->Host = $this->SMTPHost;
	// 	// 			$mailer->Port = $this->SMTPPort;
	// 	// 			$mailer->Username =$this->SMTPUserName;
	// 	// 			$mailer->Password =$this->SMTPPassword;

	// 	// 			//$mailer->SMTPSecure = "tls";
	// 	// 			//$mailer->SMTPAuth = true;

	// 	// 			$mailer->From = $this->SMTPFromEmail; 
	// 	// 			$mailer->FromName = $this->SMTPFromName;
	// 	// 			$mailer->IsHTML(true);
	// 	// 			$mailer->Body = $emailContent;
	// 	// 			$mailer->Subject = $subject;
	// 	// 			$mailer->AddAddress($to);
	// 	// 			if($attatchementPath !=""){$mailer->AddAttachment($attatchementPath);}
	// 	// 			$error = $mailer->Send();	

	// 	// 			unset($mailer);
	// 	// 			//----------------------------------------------------  
	// 	// 		}
	// 	// 		else if($this->EmailType=='M')
	// 	// 		{
	// 	// 			//Send MAIL Email 
	// 	// 			//---------------------------------------
	// 	// 			$mailer = new PHPMailer();
	// 	// 			$mailer->Mailer = "mail";
	// 	// 			$mailer->From = $this->SMTPFromEmail;  
	// 	// 			$mailer->FromName =$this->SMTPFromName; 
	// 	// 			$mailer->IsHTML(true);
	// 	// 			$mailer->Body = $emailContent;
	// 	// 			$mailer->Subject = $subject;
	// 	// 			$mailer->AddAddress($to);
	// 	// 			if($attatchementPath !=""){$mailer->AddAttachment($attatchementPath);}
	// 	// 			$mailer->Send();
	// 	// 			//----------------------------------------------------  
	// 	// 		}
	// 	// 		else if($this->EmailType=='P')
	// 	// 		{
	// 	// 			//Send PHP Email 
	// 	// 			//---------------------------------------
	// 	// 			// Always set content-type when sending HTML email
	// 	// 			$headers = "MIME-Version: 1.0" . "\r\n";
	// 	// 			$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";

	// 	// 			//Need to add attachment
	// 	// 			// More headers
	// 	// 			$headers .= 'From: <'. $this->SMTPFromEmail.'>';
	// 	// 			mail($to,$subject,$emailContent,$headers);
	// 	// 			//----------------------------------------------------  
	// 	// 		}
	// }

	function SendEmail($subject, $emailContent, $to, $attatchementPath = "")
	{

		// Mailgun configuration
		$apiKey     = CT_MAXICO;
		$domain     = CT_MAXICO_DOMAIN;
		$fromEmail  = CT_MAXICO_FROM_EMAIL;
		$fromName   = CT_MAXICO_FROM_NAME;

		// Initialize Mailgun client
		$mg = Mailgun::create($apiKey);

		// Build message payload
		$params = [
			'from'    => "$fromName <$fromEmail>",
			'to'      => $to,
			'subject' => $subject,
			'html'    => $emailContent,
			'text'    => strip_tags($emailContent)
		];

		// 	print_r($params);

		// 	// Attach file if path is valid
		// 	if (!empty($attatchementPath) && file_exists($attatchementPath)) {
		// 		$params['attachment'] = [
		// 			[
		// 				'filePath' => $attatchementPath,
		// 				'filename' => basename($attatchementPath)
		// 			]
		// 		];
		// 	}

		try {
			$response = $mg->messages()->send($domain, $params);
			return $response->getId(); // success
		} catch (Exception $e) {
			// error_log('Mailgun Exception: ' . $e->getMessage());
			// return false;
		}
	}

	function getEmailHeaderTable()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Welcome to Clinical Trac for ' . $this->currenschoolDisplayname . '</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}
	function getEmailHeaderTableForReviewMailToStudent()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Clinician Added Review To Patient Assessment/Chart Review Case Study</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}
	function getEmailHeaderTableForReviewMailToClinician()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Student Edited Patient Assessment/Chart Review Case Study</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}
	function getEmailHeaderTableForPersonnel()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Personnel CoARC Survey for ' . $this->currenschoolDisplayname . '</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}
	function getEmailHeaderTableForGraduate()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Graduate CoARC Survey for ' . $this->currenschoolDisplayname . '</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}

	function getEmailHeaderTableForEmployer()
	{
		return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
			<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Employer CoARC Survey for ' . $this->currenschoolDisplayname . '</h1>
			<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
			<td width="30%" align="center"  >
			<img width="90" height="90" src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>
		</tr>
		
		</table>';
	}


	function getEmailFooterTable()
	{
		return ''; //Temp
		/*return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
				<tr><td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Footer</h1></td>
				</tr></table>';*/
	}

	function getEmailHeaderTableNew()
	{
		// echo 'inside';
		// exit;
		// return '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// <tr>
		// 	<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Welcome to Clinical Trac for '.$this->currenschoolDisplayname.'</h1>
		// 	<b>Note: </b>Clinical Trac is best viewed using Google Chrome as your Browser</td>
		// 	<td width="30%" align="center"  >
		// 	<img width="90" height="90" src="'.$this->currenschoolLogoImagePath.'" alt="'.$this->currenschoolDisplayname.'" title="'.$this->currenschoolDisplayname.'" /></td>
		// </tr>

		// </table>';

		$newHtml = '<!DOCTYPE html>
		 <html lang="en">
		 
		 <head>
			 <meta charset="UTF-8">
			 <meta name="viewport" content="width=device-width, initial-scale=1.0">
			 <title>Document</title>
		 </head>
		 
		 <body>
			 <div style="width: 550px; max-width: 550px; margin: 0 auto;">
				 <div style="background: #01A750; ">
		 
					 <div style="margin: auto; width: fit-content;">
						 <img style="width: 150px; margin: 0 auto;"
							 src="https://rt.clinicaltrac.net/assets/images/new-email/images/logo.png" alt="">
					 </div>
					 <div
						 style="width: 90%; margin: 0 auto; border-top-left-radius: 8px; border-top-right-radius: 8px; background-color: #fff; padding:15px 0 0 0;transform: translateY(2px);">
						 <div style="width: 230px; height: 230px; margin: 0 auto;">
							 <img style="width: 100%; height: 100%; object-fit: contain;"
							 src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '">
						 </div>
					 </div>
				 </div>';
		return $newHtml;
	}

	function getEmailFooterTableNew()
	{
		return '<div style="background-color: #f7f9fb;">
		<div style="width: 90%; padding: 20px auto; margin: 0 auto; padding-top: 30px; text-align: center;">
			<p style="padding: 0 25px; padding-bottom: 20px; border-bottom: 2px solid #DEE2E9;margin-bottom: 8px;">
				<a style="text-decoration: none; font-size: 14px;
					font-style: normal;
					font-weight: 700;
					line-height: 16px; color: #0f8df0; padding: 5px 0; "
					href="https://www.facebook.com/ClinicalTrac/" target="_blank">Facebook</a>
			</p>
			<p style="color: #767E9D;padding: 0 25px; padding-top: 20px;font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
		margin: 0;">
				If you have any questions or require assistance, feel free to reach out to our dedicated support
				team!
			</p>

			<p style="color: #767E9D;padding: 0 25px; font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
		margin-bottom: 4px;"><span style="margin-bottom: 5px; line-height: 26px;">Toll Free Number
					************</span> <br>
				Clinical Trac™ 1300 S Polk, Dallas, TX 75224, United States

			</p>
			<p style="padding: 20px 25px; margin: 0;">
				<a style="text-decoration: none; font-size: 14px;
					font-style: normal;
					font-weight: 700;
					line-height: 16px; color: #767E9D; padding: 5px 0; padding-right: 10px; border-right: 2px solid #DEE2E9"
					href="https://clinicaltrac.com/aboutus.html">About Us</a>

				<a style="text-decoration: none; font-size: 14px;
					 font-style: normal;
					 font-weight: 700;
					 line-height: 16px; color: #767E9D; padding: 5px 10px; border-right: 2px solid #DEE2E9"
					href="https://clinicaltrac.com/testimonials.html" target="_blank">Testimonials </a>
				<a style="text-decoration: none; font-size: 14px;
					 font-style: normal;
					 font-weight: 700;
					 line-height: 16px; color: #767E9D; padding: 5px 10px; border-right: 2px solid #DEE2E9"
					href="https://clinicaltrac.com/privacy.html" target="_blank">Privacy policy</a>
				<a style="text-decoration: none; font-size: 14px;
					 font-style: normal;
					 font-weight: 700;
					 line-height: 16px; color: #767E9D; padding: 5px 10px;"
					href="https://clinicaltrac.com/contactus.html" target="_blank">Contact us</a>

			</p>


			<div style="padding: 0 25px; padding-bottom: 20px;">
				<p style="text-decoration: none; font-size: 14px;
				font-style: normal;
				font-weight: 700;
				line-height: 16px; color: #767E9D;margin-top: 0; margin-bottom: 10px;">Get Our App Now from</p>
				<span><a style="text-decoration: none; margin-right: 5px;"
						href="https://play.google.com/store/apps/details?id=com.clinicaltrac.app&hl=en-IN"
						target="_blank"><img style="width: 120px;"
							src="https://rt.clinicaltrac.net/assets/images/new-email/images/google-play.png" alt="">
					</a> <a style="text-decoration: none; margin-right: 5px;" href="#" target="_blank"><img
							style="width: 120px;"
							src="https://rt.clinicaltrac.net/assets/images/new-email/images/app-store.png"
							alt=""></a></span>
			</div>
		</div>
		</div>
		</div>
		</body>

		</html>';
	}

	// function SendStudentLoginDetails($studentId,$dynamicePassword)
	// {

	// 	//Read admin Details
	// 	$objDB = new clsDB();	
	// 	$sql = "Select * From  student  Where studentId=".$studentId;
	// 	$row = $objDB->GetDataRow($sql);
	// 	unset($objDB);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$UserName = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);

	// 	$subject = $this->currenschoolDisplayname." - Login Details Email Notification";
	// 	// echo 'hi';
	// 	$emailContent = $this->getEmailHeaderTableNew();

	// 	// 	$emailContent .= `<div>
	// 	// 	<div class="main-card" style="
	// 	// ">
	// 	// 	<div class="bee-block bee-block-7 bee-paragraph heading-para">
	// 	// 		<p>Clinical Trac is best viewed using <span style="color: #01A750; text-decoration: underline;"> Goolge Chrome Browser </span>
	// 	// 		</p>
	// 	// 	</div>
	// 	// 	<div class="bee-block bee-block-2 bee-divider">
	// 	// 		<div class="center bee-separator"
	// 	// 			style="border-top:1px solid #dddddd;width:100%; margin: 0;"></div>
	// 	// 	</div>
	// 	// 		<div class="bee-block bee-block-2 bee-image"><img alt="Springs logo"
	// 	// 				class="bee-center bee-fixedwidth"
	// 	// 				src="https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/1027687_1012663/image%201.png"
	// 	// 				style="max-width:250px; width: 250px; height: 250px;" /></div>
	// 	// 		<div class="bee-block bee-block-3 bee-spacer">
	// 	// 			<div class="spacer" style="height:20px;"></div>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-4 bee-heading">
	// 	// 			<h1
	// 	// 				style="color:#191847;direction:ltr;font-size:36px;font-weight:700;letter-spacing:normal;line-height:120%;text-align:left;margin-top:0;margin-bottom:0;">
	// 	// 				Welcome to Clinical Trac </h1>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-5 bee-spacer">
	// 	// 			<div class="spacer" style="height:10px;"></div>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-6 bee-heading">
	// 	// 			<h1
	// 	// 				style="color:#191847;direction:ltr;font-size:19px;font-weight:600;letter-spacing:normal;line-height:120%;text-align:left;margin-top:0;margin-bottom:0;">
	// 	// 				Hello Praful.</h1>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-7 bee-paragraph">
	// 	// 			<p>Write some exiting content here You have received a student CoArc Survery request.
	// 	// 				Please complete the surevey. Your feedback is valuable and necessary for the process
	// 	// 			</p>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-8 bee-spacer">
	// 	// 			<div class="spacer" style="height:10px;"></div>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-9 bee-heading">
	// 	// 			<h3
	// 	// 				style="color:#767676;direction:ltr;font-size:16px;font-weight:600;letter-spacing:normal;line-height:120%;text-align:left;margin-top:0;margin-bottom:0;">
	// 	// 				<span class="tinyMce-placeholder">Username : <span
	// 	// 						style="color: #000; font-weight: 700;"> Shrutika Khot </span></span>
	// 	// 			</h3>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-10 bee-heading">
	// 	// 			<h3
	// 	// 				style="color:#767676;direction:ltr;font-size:16px;font-weight:600;letter-spacing:normal;line-height:120%;text-align:left;margin-top:0;margin-bottom:0;">
	// 	// 				Temporary password :<span style="color: #000; font-weight: 700;"> Shrut123 </span>
	// 	// 			</h3>
	// 	// 		</div>

	// 	// 		<div class="bee-block bee-block-12 bee-spacer">
	// 	// 			<div class="spacer" style="height:10px;"></div>
	// 	// 		</div>

	// 	// 		<div class="bee-block bee-block-15 bee-button">
	// 	// 			<div class="bee-button-content"
	// 	// 				style="font-size: 16px; background-color: #01a750; border-bottom: 0px solid transparent; border-left: 0px solid transparent; border-radius: 8px; border-right: 0px solid transparent; border-top: 0px solid transparent; color: #ffffff; direction: ltr; font-weight: 400; max-width: 100%; padding-bottom: 5px; padding-left: 20px; padding-right: 20px; padding-top: 5px; width: 90%; display: inline-block;padding: 12px;">
	// 	// 				<span
	// 	// 					style="word-break: break-word; font-size: 16px; line-height: 24px; font-weight: 700;">Login
	// 	// 					and Continue</span>
	// 	// 			</div>
	// 	// 		</div>
	// 	// 		<div class="bee-block bee-block-16 bee-spacer">
	// 	// 			<div class="spacer" style="height:10px;"></div>
	// 	// 		</div>
	// 	// 	</div>`;
	// 	// $emailContent .= '<br/>You can click the URL below to login and continue</br>';
	// 	// $emailContent.='<br/><a href="'.$this->currenschoolURL.'/student/index.html" >'.$this->currenschoolURL.'/student/index.html</a>';
	// 	// $emailContent .= $this->getEmailFooterTableNew();
	// 		// print_r($emailContent);exit;
	// 	$this->SendEmail($subject,$emailContent,$Email);
	// }
	function SendSystemUserForgotPassword($systemUserMasterId, $dynamicePassword)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
		$row = $objDB->GetDataRow($sql);

		$phone = stripslashes($row['phone']);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$schoolId = stripslashes($row['schoolId']);
		$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $schoolId);

		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$subject = $this->currenschoolDisplayname . "- Admin Forgot Password Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{TITLE}}' => 'Forgot Password',
			'{{URL}}' => $loginUrl,
			'{{MESSAGE}}' => "We received a request to recover your account password. Below, you'll find your login details to regain access to your account:"

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $Email);

		//Send sms 

		// Create TinyUrl
		$randomUrl = getTinyUrl($loginUrl);

		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
		$body = "Dear $FirstName,\n\nWe received a request to reset your password.\n\nHere are your login credentials:\nUsername: $UserName\nPassword: $dynamicePassword\n\nPlease securely sign in to your account using the following link:\n$redirectUrl\n\nIf you didn't request a password reset, please ignore this message.";

		sendSMS('+' . $countryCode . $phone, $body);
	}

	function SendClinicianForgotPassword($clinicianId, $dynamicePassword, $currenschoolLogoImagePath, $currenschoolDisplayname, $isMobile = 0)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From clinician Where clinicianId=" . $clinicianId;

		$row = $objDB->GetDataRow($sql);

		$phone = stripslashes($row['phone']);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$schoolId = stripslashes($row['schoolId']);
		$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $schoolId);

		$loginUrl = $this->currenschoolURL . '/clinician/index.html';

		$subject = $currenschoolDisplayname . "- Clinician Forgot Password Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Forgot Password',
			'{{MESSAGE}}' => "We received a request to recover your account password. Below, you'll find your login details to regain access to your account:"

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $Email);

		//Send sms 

		// Create TinyUrl
		$randomUrl = getTinyUrl($loginUrl, $isMobile);

		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
		$body = "Dear $FirstName,\n\nWe received a request to reset your password.\n\nHere are your login credentials:\nUsername: $UserName\nPassword: $dynamicePassword\n\nPlease securely sign in to your account using the following link:\n$redirectUrl\n\nIf you didn't request a password reset, please ignore this message.";

		sendSMS('+' . $countryCode . $phone, $body);
	}

	// discard in new design
	function SendPasswordToClinician($dynamicePassword, $clinicianId, $Email, $currenschoolLogoImagePath, $currenschoolDisplayname)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From clinician Where clinicianId=" . $clinicianId;

		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$Email = stripslashes($row['email']);

		$subject = $currenschoolDisplayname . "- Password Email Notification";

		$emailContent = $this->getEmailHeaderTable($currenschoolDisplayname, $currenschoolLogoImagePath);

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
		<td valign="top">
			<p>Hello, </strong> ' . $FirstName . '</p>
			<br/>
			Below are your login details:<br/>
			<strong>Username:</strong> ' . $Username . '<br/>
			<strong>Temporary Password:</strong> ' . $dynamicePassword . '<br/>
			</p>
		</td>
		</tr>

		</table>';
		$emailContent .= '<br/>You can click the URL below to login and continue</br>';
		$emailContent .= '<br/><a href="' . $this->currenschoolURL . '/clinician/index.html" >' . $this->currenschoolURL . '/clinician/index.html</a>';

		$emailContent .= $this->getEmailFooterTable();
		//print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $Email);
	}

	function SendstudentForgotPassword($studentId, $dynamicePassword, $currenschoolLogoImagePath, $currenschoolDisplayname, $isMobile = 0)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From student Where studentId=" . $studentId;

		$row = $objDB->GetDataRow($sql);
		// print_r($row);exit;

		$phone = stripslashes($row['phone']);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$schoolId = stripslashes($row['schoolId']);
		$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $schoolId);

		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $currenschoolDisplayname . "- Student Forgot Password Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Forgot Password',
			'{{MESSAGE}}' => "We received a request to recover your account password. Below, you'll find your login details to regain access to your account:"

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;

		$this->SendEmail($subject, $emailContent, $Email);


		//Send sms 

		// Create TinyUrl
		$randomUrl = getTinyUrl($loginUrl, $isMobile);

		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
		$body = "Dear $FirstName,\n\nWe received a request to reset your password.\n\nHere are your login credentials:\nUsername: $UserName\nPassword: $dynamicePassword\n\nPlease securely sign in to your account using the following link:\n$redirectUrl\n\nIf you didn't request a password reset, please ignore this message.";

		sendSMS('+' . $countryCode . $phone, $body);
	}

	// discard in new design
	function SendPasswordToStudent($password, $studentId, $emailId, $currenschoolLogoImagePath, $currenschoolDisplayname)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From student Where studentId=" . $studentId;

		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$Email = stripslashes($row['email']);

		$subject = $currenschoolDisplayname . "-Password  Notification";

		$emailContent = $this->getEmailHeaderTable($currenschoolDisplayname, $currenschoolLogoImagePath);

		$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		<tr>
		<td valign="top">
			<p>Hello, </strong> ' . $FirstName . '</p>
			
			Below are your login details.<br/>
			<strong>Username:</strong> ' . $Username . '<br/>
			<strong>Temporary Password:</strong> ' . $password . '<br/>
			</p>
		</td>
		</tr>

		</table>';
		$emailContent .= '<br/>You can click the URL below to login and continue</br>';
		$emailContent .= '<br/><a href="' . $this->currenschoolURL . '/student/index.html" >' . $this->currenschoolURL . '/student/index.html</a>';

		$emailContent .= $this->getEmailFooterTable();
		// print_r($emailContent);exit;	
		$this->SendEmail($subject, $emailContent, $Email);
	}

	// Send Email for system user
	function SendSystemUserLoginDetails($systemUserMasterId, $dynamicePassword)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$subject = $this->currenschoolDisplayname . " - Login Details";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/loginDetails.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
	}

	function SendWelcomeEmailtoAdmin($dynamicePassword)
	{
		// echo "hi";
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  systemusermaster Where isPrimaryUser='1' AND schoolId=" . $this->schoolId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Password = stripslashes($row['passwordHash']);
		$Email = stripslashes($row['email']);
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$subject = "ClinicalTrac - Welcome Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/newSchoolRegisteration.html');

		// $emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// 					<tr>
		// 						<td width="70%" valign="top"><h1 style="font-family:\'Trebuchet MS\', Arial, Helvetica, sans-serif" >Thank you for registering to the ' . APPLICATION_NAME . '</h1></td>
		// 					<td width="30%" align="center"  >
		// 						<img src="' . $this->currenschoolLogoImagePath . '" alt="' . $this->currenschoolDisplayname . '" title="' . $this->currenschoolDisplayname . '" /></td>

		// 					</tr>

		// 					<tr>
		// 						<td vealign="top">
		// 							<p><h3>Welcome to Clinical Trac for "' . $this->currenschoolDisplayname . '"</h3></p>
		// 							<p><b>Note:</b> Clinical Trac is best viewed using Google Chrome as your Browser</p>
		// 							<p>Hello, <strong> ' . $FirstName . '</strong></p>
		// 							<p>Below are your login details.<br/>
		// 							<strong>Username:</strong> ' . $UserName . '<br/>
		// 							<strong>Temporary Password:</strong> ' . $dynamicePassword . '<br/>
		// 							</p>
		// 						</td>
		// 					</tr>
		// 				</table>';
		// $emailContent .= '<br/>You can click the URL below to login and continue';
		// $emailContent .= '<br/><a href="' . $this->currenschoolURL . '/admin/index.html" >' . $this->currenschoolURL . '/admin/index.html</a>';
		// $emailContent .= $this->getEmailFooterTable();
		// print_r($emailContent);exit;
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{APPLICATION_NAME}}' => APPLICATION_NAME,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;

		$this->SendEmail($subject, $emailContent, $Email);
		$this->SendEmail($subject, $emailContent, '<EMAIL>');
		$this->SendEmail($subject, $emailContent, '<EMAIL>');
	}


	// Send Email for Clinician
	function SendClinicianLoginDetails($clinicianId, $dynamicePassword)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  clinician Where clinicianId=" . $clinicianId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/clinician/index.html';

		$subject = $this->currenschoolDisplayname . " - Login Details";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/loginDetails.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		$this->SendEmail($subject, $emailContent, $Email);
	}

	// Send Email for clinician when admin change the password
	function SendClinicianChangePasswordDetails($clinicianId, $dynamicePassword)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  clinician Where clinicianId=" . $clinicianId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/clinician/index.html';

		$subject = $this->currenschoolDisplayname . " - Change Password Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Password Changed!',
			'{{MESSAGE}}' => 'This is to confirm that the password for your account has been changed by School Admin. Below are your login details.'
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');
		$this->SendEmail($subject, $emailContent, $Email);
	}

	// Send Email for student 
	function SendStudentLoginDetails($studentId, $dynamicePassword)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "Select * From  student  Where studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . " - Login Details";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/loginDetails.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $this->SendEmail($subject,$q	emailContent,'<EMAIL>');exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
		// $status = ($sendEmail == '202') ? 1 : 0;
	}

	// Send Email for student when admin change the password
	function SendStudentChangePasswordDetails($studentId, $dynamicePassword)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "Select * From  student  Where studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . " - Change Password Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Password Changed!',
			'{{MESSAGE}}' => 'This is to confirm that the password for your account has been changed by School Admin. Below are your login details.'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');exit;
		echo $sendEmail = $this->SendEmail($subject, $emailContent, $Email);
		$status = ($sendEmail == '202') ? 1 : 0;
	}

	// function SendStudentLoginDetails1($studentId, $dynamicePassword)
	// {
	// 	// // Read the email template file
	// 	// $emailTemplate = file_get_contents(__DIR__  . '/../includes/email_template.html');
	// 	// $subject = $this->currenschoolDisplayname." - Login Details Email Notification";

	// 	// // Replace placeholders with dynamic data
	// 	// $studentName = "Sourabh Kashid"; // Replace this with the actual student's name
	// 	// $tempUsername = "Sourabh@8770"; // Replace this with the actual login details
	// 	// $email = "<EMAIL>"; // Replace this with the actual login details
	// 	// $emailTemplate = str_replace('{{FULL_NAME}}', $studentName, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{TEMP_USERNAME}}', $tempUsername, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{EMAIL}}', $email, $emailTemplate);
	// 	// // $emailTemplate = str_replace("{{student_name}}", $studentName, $emailTemplate);
	// 	// // $emailTemplate = str_replace("{{login_details}}", $loginDetails, $emailTemplate);
	// 	// 	print_r($emailTemplate);exit;
	// 	// $this->SendEmail($subject,$emailTemplate,'<EMAIL>');

	// 	//Read admin Details
	// 	$objDB = new clsDB();
	// 	$sql = "Select * From  student  Where studentId=" . $studentId;
	// 	$row = $objDB->GetDataRow($sql);
	// 	unset($objDB);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$UserName = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);
	// 	$fullName = $FirstName . ' ' . $LastName;
	// 	$loginUrl = $this->currenschoolURL . '/student/index.html';

	// 	$subject = $this->currenschoolDisplayname . " - Login Details Email Notification";
	// 	$emailTemplate = file_get_contents(__DIR__  . '/../includes/email_templates/loginDetails.html');

	// 	// $emailTemplate = str_replace('{{NAME}}', $FirstName, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{FULL_NAME}}', $fullName, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{SCHOOL_NAME}}', $this->currenschoolDisplayname, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{SCHOOL_LOGO}}', $this->currenschoolLogoImagePath, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{USERNAME}}', $UserName, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{PASSWORD}}', $dynamicePassword, $emailTemplate);
	// 	// $emailTemplate = str_replace('{{URL}}', $loginUrl, $emailTemplate);

	// 	// Define the associative array with placeholders and their corresponding replacement values
	// 	$replaceValues = array(
	// 		'{{NAME}}' => $FirstName,
	// 		'{{FULL_NAME}}' => $fullName,
	// 		'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
	// 		'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath.'?rand='.rand(1, 100),
	// 		'{{USERNAME}}' => $UserName,
	// 		'{{PASSWORD}}' => $dynamicePassword,
	// 		'{{URL}}' => $loginUrl
	// 	);

	// 	// Perform the replacements in one go using str_replace with arrays
	// 	$emailTemplate = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailTemplate);

	// 	print_r($emailTemplate);
	// 	exit;
	// 	$sendEmail = $this->SendEmail($subject, $emailTemplate, '<EMAIL>');
	// 	$status = ($sendEmail == '202') ? 1 : 0;
	// 	exit;
	// 	// $subject = $this->currenschoolDisplayname." - Login Details Email Notification New";
	// 	$emailContent = $this->getEmailHeaderTableNew();
	// 	$emailContent .= '<div style="background-color: #f7f9fb;">
	// 	<div
	// 		style="width: 90%; margin: 0 auto; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; background-color: #fff; box-shadow: 0px 20px 25px -5px rgba(0, 0, 0, 0.04);">
	// 		<div style="padding:0 25px 15px 25px;">
	// 			<div style="padding: 20px 0">
	// 				<h1 style="font-size: 36px; font-style: normal; font-weight: 700; line-height: 100%; /* 36px */ letter-spacing: -0.576px; color: #191847; /* margin-top: 40px; */ margin: 0; ">
	// 				Login Credentials</h1>
	// 			</div>
	// 			<div>
	// 				<div>
	// 					<h5 style="font-size: 19px; font-style: normal; font-weight: 600; line-height: 26px; color: #191847; margin: 8px 0;">
	// 						Dear ' . $FirstName . '
	// 					</h5>
	// 					<p style="font-size: 17px; font-style: normal; font-weight: 400; color: #191847; line-height: 22px; margin: 0;">
	// 					Welcome to ' . $this->currenschoolDisplayname . '
	// 					</p>
	// 					<p style="font-size: 17px; font-style: normal; font-weight: 400; color: #191847; line-height: 22px; margin: 0;">
	// 					Below are your login credentials:
	// 					</p>
	// 				</div>
	// 				<div style="margin: 30px 0; margin-bottom: 10px;">
	// 					<!-- ... (previous HTML content) ... -->

	// 					<p
	// 						style="color: #767676; font-size: 16px; font-style: normal; font-weight: 600; padding: 0; margin: 7px 0;">
	// 						Full name : <span style="color: #000;">' . $FirstName . ' ' . $LastName . '</span></p>
	// 					<p
	// 						style="color: #767676; font-size: 16px; font-style: normal;font-weight: 600; padding: 0; margin: 7px 0;">
	// 						Temporary username : <span style="color: #000;">' . $UserName . '</span></p>
	// 					<p
	// 						style="color: #767676;font-size: 16px; font-style: normal; font-weight: 600; padding: 0;margin: 7px 0;">
	// 						Temporary Password : <span style="color: #000;">' . $dynamicePassword . '</span></p>

	// 				</div>

	// 				<div style="border-bottom: 2px solid #E2E2E2;">
	// 					<p style="color: #000; font-size: 17px; font-style: normal; font-weight: 500; line-height: 26px; margin-top: 0; ">
	// 						<span style="margin-bottom: 10px;line-height: 36px;"> Regards,</span> <br>
	// 						' . $this->currenschoolDisplayname . '
	// 						</br>
	// 				</div>
	// 				<div>
	// 					<a href = "' . $this->currenschoolURL . '/student/index.html">
	// 						<button style="border-radius: 8px; background: #01A750; padding: 12px 24px; max-width: 450px; width: 100%; outline: none; border: none;color: #fff;cursor: pointer;font-size: 16px;font-style: normal;font-weight: 700;line-height: 24px; margin-top: 15px; " type="button">
	// 							Login and Continue
	// 						</button>
	// 					</a>
	// 				</div>
	// 			</div>
	// 		</div>

	// 	</div>
	// 	</div>';
	// 	$emailContent .= $this->getEmailFooterTableNew();
	// 	// $this->SendEmail($subject,$emailContent,'<EMAIL>');
	// 	print_r($emailContent);
	// 	exit;
	// }
	// function NotifyToAdminClinicianLockedOut($clinicianId)
	// {
	// 	//Read admin Details
	// 	$objDB = new clsDB();
	// 	$sql = "Select * From  clinician Where clinicianId=" . $clinicianId;
	// 	$row = $objDB->GetDataRow($sql);
	// 	unset($objDB);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$FullName = $FirstName . ' ' . $LastName;
	// 	$UserName = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);

	// 	$subject = $this->currenschoolDisplayname . " - Clinician Locked Out Email Notification";
	// 	$emailContent = $this->getEmailHeaderTable();

	// 	$emailContent = "Dear Admin,";
	// 	$emailContent .= "<p>You have received a notification for a Blocked clinician needing a
	// 						password reset. Please see information below:<br><br>";
	// 	$emailContent .= "<strong>FullName</strong>:" . $FullName . "<br>";
	// 	$emailContent .= "<strong>Temporary Username</strong>: " . $UserName . "<br>";
	// 	$emailContent .= "<strong>Email</strong>: " . $Email . "</p>";
	// 	//$emailContent .= "<p>Please login to ".$SystemUserLoginLink." to reset the password</p>";
	// 	$emailContent .= "<p>&nbsp;</p>";
	// 	$emailContent .= "<p>Regards,<br/>";
	// 	$emailContent .= "<strong>" . $this->currenschoolDisplayname . "</strong></p>";

	// 	$emailContent .= '<br/>You can click the URL below to login and continue';
	// 	$emailContent .= '<br/><a href="' . $this->currenschoolURL . '/clinician/index.html" >' . $this->currenschoolURL . '/clinician/index.html</a>';
	// 	$emailContent .= $this->getEmailFooterTable();
	// 	//echo $emailContent;exit;
	// 	$this->SendEmail($subject, $emailContent, $Email);
	// }

	// function NotifyToAdminStudentLockedOut($studentId)
	// {
	// 	//Read admin Details
	// 	$objDB = new clsDB();
	// 	$sql = "Select * From  student Where studentId=" . $studentId;
	// 	$row = $objDB->GetDataRow($sql);
	// 	unset($objDB);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$FullName = $FirstName . ' ' . $LastName;
	// 	$UserName = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);

	// 	$subject = $this->currenschoolDisplayname . " - Student Locked Out Email Notification";
	// 	$emailContent = $this->getEmailHeaderTable();

	// 	$emailContent = "Dear Admin,";
	// 	$emailContent .= "<p>You have received a notification for a Blocked student needing a
	// 						password reset. Please see information below:<br><br>";
	// 	$emailContent .= "<strong>FullName</strong>:" . $FullName . "<br>";
	// 	$emailContent .= "<strong>Temporary Username</strong>: " . $UserName . "<br>";
	// 	$emailContent .= "<strong>Email</strong>: " . $Email . "</p>";
	// 	//$emailContent .= "<p>Please login to ".$SystemUserLoginLink." to reset the password</p>";
	// 	$emailContent .= "<p>&nbsp;</p>";
	// 	$emailContent .= "<p>Regards,<br/>";
	// 	$emailContent .= "<strong>" . $this->currenschoolDisplayname . "</strong></p>";

	// 	$emailContent .= '<br/>You can click the URL below to login and continue';
	// 	$emailContent .= '<br/><a href="' . $this->currenschoolURL . '/student/index.html" >' . $this->currenschoolURL . '/student/index.html</a>';
	// 	$emailContent .= $this->getEmailFooterTable();

	// 	$this->SendEmail($subject, $emailContent, $Email);
	// }

	function NotifyToStudentAndDCEStudentLockedOut($studentId)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "Select * From  student  Where studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$currentSchoolId = stripslashes($row['schoolId']);
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . " - Student Account Blocked Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{EMAIL}}' => $Email,
			'{{URL}}' => $loginUrl,
			'{{MESSAGE}}' => 'Your account is blocked due to multiple login attempts. We have sent notification to Admin to reset your password. <br> Please see information below:'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);

		$sql = "SELECT systemusermaster.firstName, systemusermaster.lastName, systemusermaster.email FROM systemuserrolemaster 
				Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE systemuserrolemaster.title ='D.C.E.' AND systemuserrolemaster.schoolId = " . $currentSchoolId;
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {

			$Name = stripslashes($Systemrow['firstName']);
			$DCEEmail = stripslashes($Systemrow['email']);
			$FirstName = stripslashes($row['firstName']);
			$LastName = stripslashes($row['lastName']);
			$UserName = stripslashes($row['username']);
			$fullName = $FirstName . ' ' . $LastName;
			$Email = stripslashes($row['email']);

			// $subject = $currenschoolDisplayname . "- Student Account Blocked Email Notification";

			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $Name,
				'{{FULL_NAME}}' => $fullName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{USERNAME}}' => $UserName,
				'{{EMAIL}}' => $Email,
				'{{URL}}' => $loginUrl,
				'{{MESSAGE}}' => 'You have received a notification for a Blocked student needing a password reset. 
				                  <br> Please see information below:'
			);
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
			// print_r($emailContent);
			// exit;
			$sendEmail = $this->SendEmail($subject, $emailContent, $DCEEmail);
		}
		$status = ($sendEmail == '202') ? 1 : 0;
		unset($objDB);
	}

	function NotifyToClinicianAndDCEClinicianLockedOut($clinicianId)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  clinician Where clinicianId=" . $clinicianId;
		$row = $objDB->GetDataRow($sql);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$fullName = $FirstName . ' ' . $LastName;
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$currentSchoolId = stripslashes($row['schoolId']);

		$loginUrl = $this->currenschoolURL . '/clinician/index.html';
		$subject = $this->currenschoolDisplayname . " - Clinician Account Blocked Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{EMAIL}}' => $Email,
			'{{URL}}' => $loginUrl,
			'{{MESSAGE}}' => 'Your account is blocked due to multiple login attempts. We have sent notification to Admin to reset your password. <br> Please see information below:'

		);
		//echo $emailContent;exit;
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);

		$sql = "SELECT systemusermaster.firstName, systemusermaster.lastName, systemusermaster.email FROM systemuserrolemaster 
				Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE systemuserrolemaster.title ='D.C.E.' AND systemuserrolemaster.schoolId = " . $currentSchoolId;
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {

			$Name = stripslashes($Systemrow['firstName']);
			$DCEEmail = stripslashes($Systemrow['email']);
			$FirstName = stripslashes($row['firstName']);
			$LastName = stripslashes($row['lastName']);
			$UserName = stripslashes($row['username']);
			$fullName = $FirstName . ' ' . $LastName;
			$Email = stripslashes($row['email']);

			// $subject = $currenschoolDisplayname . "- Clinician Account Blocked Email Notification";

			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $Name,
				'{{FULL_NAME}}' => $fullName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{USERNAME}}' => $UserName,
				'{{EMAIL}}' => $Email,
				'{{URL}}' => $loginUrl,
				'{{MESSAGE}}' => 'You have received a notification for a Blocked clinician needing a password reset.<br> Please see information below:'
			);
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
			// print_r($emailContent);
			// exit;
			$sendEmail = $this->SendEmail($subject, $emailContent, $DCEEmail);
		}
		$status = ($sendEmail == '202') ? 1 : 0;
		unset($objDB);
	}


	// function NotifyToAdminUserLockedOut($systemUserMasterId)
	// {
	// 	//Read admin Details
	// 	$objDB = new clsDB();
	// 	$sql = "Select * From  systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
	// 	$row = $objDB->GetDataRow($sql);
	// 	unset($objDB);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$FullName = $FirstName . ' ' . $LastName;
	// 	$UserName = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);

	// 	$subject = $this->currenschoolDisplayname . " - User Locked Out Email Notification";
	// 	$emailContent = $this->getEmailHeaderTable();

	// 	$emailContent = "Dear Admin,";
	// 	$emailContent .= "<p>You have received a notification for a Blocked User needing a
	// 						password reset. Please see information below:<br><br>";
	// 	$emailContent .= "<strong>FullName</strong>:" . $FullName . "<br>";
	// 	$emailContent .= "<strong>Temporary Username</strong>: " . $UserName . "<br>";
	// 	$emailContent .= "<strong>Email</strong>: " . $Email . "</p>";
	// 	//$emailContent .= "<p>Please login to ".$SystemUserLoginLink." to reset the password</p>";
	// 	$emailContent .= "<p>&nbsp;</p>";
	// 	$emailContent .= "<p>Regards,<br/>";
	// 	$emailContent .= "<strong>" . $this->currenschoolDisplayname . "</strong></p>";

	// 	$emailContent .= '<br/>You can click the URL below to login and continue';
	// 	$emailContent .= '<br/><a href="' . $this->currenschoolURL . '/student/index.html" >' . $this->currenschoolURL . '/student/index.html</a>';
	// 	$emailContent .= $this->getEmailFooterTable();

	// 	$this->SendEmail($subject, $emailContent, $Email);
	// }

	function NotifyToAdminUserLockedOut($systemUserMasterId)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$fullName = $FirstName . ' ' . $LastName;
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$currentSchoolId = stripslashes($row['schoolId']);

		$loginUrl = $this->currenschoolURL . '/admin/index.html';
		$subject = $this->currenschoolDisplayname . " - School Admin Account Blocked Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{EMAIL}}' => $Email,
			'{{URL}}' => $loginUrl,
			'{{MESSAGE}}' => 'Your account is blocked due to multiple login attempts. We have sent notification to Admin to reset your password. <br> Please see information below:'

		);
		//echo $emailContent;exit;
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
		$sql = "SELECT * FROM schools where schools.isSuperAdmin = 1";
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {

			$Name = stripslashes($Systemrow['contactPerson']);
			$SuperAdminEmail = stripslashes($Systemrow['contactEmail']);
			$FirstName = stripslashes($row['firstName']);
			$LastName = stripslashes($row['lastName']);
			$UserName = stripslashes($row['username']);
			$fullName = $FirstName . ' ' . $LastName;
			$Email = stripslashes($row['email']);

			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/blockAccount.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $Name,
				'{{FULL_NAME}}' => $fullName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{USERNAME}}' => $UserName,
				'{{EMAIL}}' => $Email,
				'{{URL}}' => $loginUrl,
				'{{MESSAGE}}' => 'You have received a notification for a Blocked user needing a password reset. <br>Please see information below:'
			);
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
			// print_r($emailContent);
			// exit;
			$sendEmail = $this->SendEmail($subject, $emailContent, $SuperAdminEmail);
		}
		$status = ($sendEmail == '202') ? 1 : 0;
		unset($objDB);
	}

	function SendEmailToStudentForCoarcSurvey($studentId, $coarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $coarcSurveyTitle = '')
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From student Where studentId=" . $studentId;
		//echo 'sql->'.$sql;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$currentSchoolId = stripslashes($row['schoolId']);
		$Email = stripslashes($row['email']);
		$FullName = $FirstName . ' ' . $LastName;

		$link = $this->currenschoolURL . '/student/login_CoARC_Survey.html';
		$URL = $link . '?studentId=' . EncodeQueryData($studentId) . '&coarcId=' . EncodeQueryData($coarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId);
		$randomUrl = getTinyUrl($URL);
		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);

		$subject = $this->currenschoolDisplayname . "- Student CoARC Survey Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/survey.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{SURVEY_NAME}}' => 'Student',
			'{{URL}}' => $redirectUrl,
			'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
			'{{TITLE}}' => 'Student CoARC Survey'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		// $this->SendEmail($subject, $emailContent, '<EMAIL>');exit;
		$this->SendEmail($subject, $emailContent, $Email);
		echo "send";
		exit;
	}

	function SendNotificationToPDAndDCEForStudentCoarcSurveyCompleted($studentId, $coarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $schoolId, $coarcSurveyTitle = '')
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select student.*,rankmaster.rankId,rankmaster.title
		From student 
		LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
		Where studentId=" . $studentId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$FullName = $FirstName . ' ' . $LastName;
		$ranktitle = stripslashes($row['title']);

		$sql = "SELECT systemusermaster.* FROM systemuserrolemaster 
				Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE (systemuserrolemaster.title ='D.C.E.' OR systemuserrolemaster.title ='P.D.') AND systemusermaster.isActive = 1  AND systemuserrolemaster.schoolId=" . $schoolId;
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {
			$SystemUserName = stripslashes($Systemrow['firstName']);
			$SystemUserLastName = stripslashes($Systemrow['lastName']);
			$SystemUsername = stripslashes($Systemrow['username']);
			$Email = stripslashes($Systemrow['email']);
			$loginUrl = $this->currenschoolURL . '/admin/index.html';

			$subject = $currenschoolDisplayname . " - Student CoARC Survey Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/surveyDetails.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $SystemUserName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{URL}}' => $loginUrl,
				'{{SURVEY_NAME}}' => 'Student',
				'{{FULL_NAME}}' => $FullName,
				'{{RANK}}' => $ranktitle,
				'{{TITLE}}' => 'Student CoARC Survey',
				'{{SURVEY_TYPE}}' => 'Student',
				'{{DISPLAY_RANK}}' => 'display:block;',
				'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
				'{{DISPLAY_EMPLOYEE}}' => 'display:none;'

			);

			// Perform the replacements in one go using str_replace with arrays
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
			// print_r($emailContent);
			// exit;

			// $this->SendEmail($subject, $emailContent, '<EMAIL>');
			$this->SendEmail($subject, $emailContent, $Email);
		}
		// exit;
	}

	function SendEmailToStudentForGraduateCoarcSurvey($studentId, $graduatecoarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $coarcSurveyTitle = '')
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From student Where studentId=" . $studentId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$currentSchoolId = stripslashes($row['schoolId']);
		$FullName = $FirstName . ' ' . $LastName;

		include_once("../class/PasswordHash.php");

		$subject = $this->currenschoolDisplayname . " - Graduate CoARC Survey Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
		$link = '';
		//$link ="http://localhost/clinicaltrac_new/school/jackson-comm-coll-3/admin/addcoarcsurvey.html";
		//$link="https://staging.clinicaltrac.net/school/jackson-comm-coll-3/student/index.html";
		$link = $this->currenschoolURL . '/student/login_CoARC_Survey.html';
		$URL = $link . '?studentId=' . EncodeQueryData($studentId) . '&coarcId=' . EncodeQueryData($graduatecoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId) . '&type=G';
		$randomUrl = getTinyUrl($URL);
		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);

		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/survey.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{SURVEY_NAME}}' => 'Graduate',
			'{{URL}}' => $redirectUrl,
			'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
			'{{TITLE}}' => 'Graduate CoARC Survey'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		$this->SendEmail($subject, $emailContent, $Email);
	}

	function SendNotificationToPDAndDCEForGraduateCoarcSurveyCompleted($studentId, $graduatecoarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $schoolId, $coarcSurveyTitle = '')
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select student.*,rankmaster.rankId,rankmaster.title
		From student 
		LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
		Where studentId=" . $studentId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$FullName = $FirstName . ' ' . $LastName;
		$ranktitle = stripslashes($row['title']);
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$sql = "SELECT systemusermaster.* FROM systemuserrolemaster 
				Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE (systemuserrolemaster.title ='D.C.E.' OR systemuserrolemaster.title ='P.D.') AND systemusermaster.isActive = 1  AND systemuserrolemaster.schoolId=" . $schoolId;
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {
			$SystemUserName = stripslashes($Systemrow['firstName']);
			$SystemUserLastName = stripslashes($Systemrow['lastName']);
			$SystemUsername = stripslashes($Systemrow['username']);
			$Email = stripslashes($Systemrow['email']);

			$subject = $currenschoolDisplayname . " - Graduate CoARC Email Notification - " . $coarcSurveyTitle . " - " . $FullName;

			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/surveyDetails.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $SystemUserName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{URL}}' => $loginUrl,
				'{{SURVEY_NAME}}' => 'Graduate Student',
				'{{FULL_NAME}}' => $FullName,
				'{{RANK}}' => $ranktitle,
				'{{TITLE}}' => 'Graduate CoARC Survey',
				'{{SURVEY_TYPE}}' => 'Student',
				'{{DISPLAY_RANK}}' => 'display:block;',
				'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
				'{{DISPLAY_EMPLOYEE}}' => 'display:none;'

			);

			// Perform the replacements in one go using str_replace with arrays
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

			// print_r($emailContent);exit;	
			$this->SendEmail($subject, $emailContent, $Email);
		}
	}


	function SendEmailToStudentForEmployerCoarcSurvey($studentId, $employercoarcId, $emailId, $currenschoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $dynamicePassword, $coarcSurveyTitle = '')
	{
		// echo 'studentId'.$studentId;
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From employercoarcrequestmaster Where studentId=" . $studentId . "  ORDER BY employercoarcId DESC LIMIT 1";
		// echo 'sql->'.$sql;
		$row = $objDB->GetDataRow($sql);
		// print_r($row);exit;
		$FirstName = stripslashes($row['employerName']);
		// $Username = stripslashes($row['username']);
		// $currentSchoolId = stripslashes($row['schoolId']);
		$sql = "Select * From student Where studentId=" . $studentId;
		//echo 'sql->'.$studentId;
		$studentRow = $objDB->GetDataRow($sql);
		$Username = stripslashes($studentRow['username']);
		$currentSchoolId = stripslashes($studentRow['schoolId']);
		$FullName = stripslashes($studentRow['firstName']) . ' ' . stripslashes($studentRow['lastName']);

		$link = '';
		//$link ="http://localhost/clinicaltrac_new/school/jackson-comm-coll-3/admin/addcoarcsurvey.html";
		//$link="https://staging.clinicaltrac.net/school/jackson-comm-coll-3/admin/index.html";
		$link = $this->currenschoolURL . '/student/login_CoARC_Survey.html';
		// $URL=$link.'?studentId='.EncodeQueryData($studentId).'&employerlcoarcId='.EncodeQueryData($employercoarcId).'&coarcSurveyMasterId='.EncodeQueryData($coarcSurveyMasterId).'&userName='.$Username.'&schoolId='.EncodeQueryData($currentSchoolId).'&type=E';
		$URL = $link . '?studentId=' . EncodeQueryData($studentId) . '&coarcId=' . EncodeQueryData($employercoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId) . '&type=E';

		$subject = $this->currenschoolDisplayname . " - Employer CoARC Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/survey.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{SURVEY_NAME}}' => 'Employer',
			'{{URL}}' => $URL,
			'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
			'{{TITLE}}' => 'Employer CoARC Survey'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		// $this->SendEmail($subject, $emailContent, '<EMAIL>');exit;
		$this->SendEmail($subject, $emailContent, $emailId);
	}
	function SendNotificationToPDAndDCEForEmployerCoarcSurveyCompleted($studentId, $employercoarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $schoolId, $coarcSurveyTitle = '')
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select student.*,rankmaster.rankId,rankmaster.title,studentadditationlcontactinformation.ContactFirstName,studentadditationlcontactinformation.ContactLastName
				From student 
				LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
				LEFT JOIN studentadditationlcontactinformation ON student.studentId=studentadditationlcontactinformation.studentId
				
				Where student.studentId=" . $studentId;
		// echo 'sql->'.$sql;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$ranktitle = stripslashes($row['title']);
		$FullName = $FirstName . ' ' . $LastName;

		$contactName = stripslashes($row['ContactFirstName']) . ' ' . stripslashes($row['ContactLastName']);
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$sql = "SELECT systemusermaster.* FROM systemuserrolemaster 
		Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE (systemuserrolemaster.title ='D.C.E.' OR systemuserrolemaster.title ='P.D.') AND systemusermaster.isActive = 1  AND systemuserrolemaster.schoolId=" . $schoolId;
		$GetSystemData = $objDB->GetResultset($sql);

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {
			$SystemUserName = stripslashes($Systemrow['firstName']);
			$SystemUserLastName = stripslashes($Systemrow['lastName']);
			$SystemUsername = stripslashes($Systemrow['username']);
			$Email = stripslashes($Systemrow['email']);

			$subject = $currenschoolDisplayname . "- Employer CoARC Email Notification - " . $coarcSurveyTitle . " - " . $FullName;

			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/surveyDetails.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $SystemUserName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{URL}}' => $loginUrl,
				'{{SURVEY_NAME}}' => 'Employer',
				'{{FULL_NAME}}' => $FullName,
				'{{RANK}}' => $ranktitle,
				'{{TITLE}}' => 'Employer CoARC Survey',
				'{{SURVEY_TYPE}}' => 'Employee',
				'{{DISPLAY_RANK}}' => 'display:block;',
				'{{EMPLOYEE_NAME}}' => $contactName,
				'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
				'{{DISPLAY_EMPLOYEE}}' => 'display:block;'


			);
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
			// $emailContent = $this->getEmailHeaderTableForEmployer($currenschoolDisplayname, $currenschoolLogoImagePath);

			// $emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
			// 			<tr>
			// 			<td valign="top">
			// 				<p>Hello </strong> ' . $SystemUserName . ' ,</p>
			// 				<p>
			// 				You received a Employer COARC Survey Notification Email.
			// 				<br/><br/>
			// 				Below Student Completed CoARC Survey.<br/>
			// 				<strong>Student Name:</strong> ' . $FirstName . '<br/>			
			// 				<strong>Rank:</strong> ' . $ranktitle . '<br/>			
			// 				</p>
			// 			</td>
			// 			</tr>

			// 			</table>';
			// $emailContent .= $this->getEmailFooterTable();
			// print_r($emailContent);
			// exit;
			$this->SendEmail($subject, $emailContent, $Email);
		}
	}

	function SendEmailToStudentForPersonnelCoarcSurvey($clinicianId, $personnelCoarcId, $emailId, $currenschoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $coarcSurveyTitle = '')
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From clinician Where clinicianId=" . $clinicianId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$currentSchoolId = stripslashes($row['schoolId']);
		$FullName = $FirstName . ' ' . $LastName;

		//Update Password
		// include_once("../class/PasswordHash.php"); 
		// $password = GenerateRandomAlphaNumericNumber(5);
		// $passwordHash = PasswordHash::hash($password);
		// $sql = "UPDATE clinician SET passwordHash = '".($passwordHash)."' WHERE clinicianId= " . $clinicianId;
		// $objDB->ExecuteQuery($sql);	

		$subject = $this->currenschoolDisplayname . "- Personnel CoARC Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
		$link = '';
		//$link ="http://localhost/clinicaltrac_new/school/jackson-comm-coll-3/admin/addcoarcsurvey.html";
		//$link="https://staging.clinicaltrac.net/school/jackson-comm-coll-3/admin/index.html";
		$link = $this->currenschoolURL . '/clinician/login_CoARC_Survey.html';
		// $URL=$link.'?clinicianId='.EncodeQueryData($clinicianId).'&personnelcoarcId='.EncodeQueryData($personnelCoarcId).'&coarcSurveyMasterId='.EncodeQueryData($coarcSurveyMasterId);
		$URL = $link . '?clinicianId=' . EncodeQueryData($clinicianId) . '&personnelcoarcId=' . EncodeQueryData($personnelCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId);
		$randomUrl = getTinyUrl($URL);
		$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
		// $emailContent = $this->getEmailHeaderTableForPersonnel($currenschoolDisplayname, $currenschoolLogoImagePath);

		// $emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// <tr>
		// <td valign="top">
		// 	<p>Hello </strong> ' . $FirstName . ',</p>
		// 	<p>
		// 	You received a Personnel CoARC Survey email request.

		// 	<br/><br/>
		// 	<font color="red">Make sure you are already logged off Clinical Trac before proceeding!</font>
		// 	<br/><br/>
		// 	Please <a href=' . $redirectUrl . '>Click here</a> to submit your survey. 
		// 	<br/><br/>
		// 	Your feedback is valuable and necessary for the process.		
		// 	</p>
		// </td>
		// </tr>

		// </table>';
		// $emailContent .= $this->getEmailFooterTable();
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/survey.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{SURVEY_NAME}}' => 'Personnel',
			'{{URL}}' => $redirectUrl,
			'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
			'{{TITLE}}' => 'Personnel CoARC Survey'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;	
		$this->SendEmail($subject, $emailContent, $emailId);
	}

	function SendNotificationToPDAndDCEForPersonnelCoarcSurveyCompleted($clinicianId, $personnelCoarcId, $currenschoolLogoImagePath, $currenschoolDisplayname, $schoolId, $coarcSurveyTitle = '')
	{
		$ranktitle = '';
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From clinician Where clinicianId=" . $clinicianId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Username = stripslashes($row['username']);
		$FullName = $FirstName . ' ' . $LastName;

		$sql = "SELECT systemusermaster.* FROM systemuserrolemaster 
		Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId WHERE (systemuserrolemaster.title ='D.C.E.' OR systemuserrolemaster.title ='P.D.') AND systemusermaster.isActive = 1  AND systemuserrolemaster.schoolId=" . $schoolId;
		$GetSystemData = $objDB->GetResultset($sql);
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		while ($Systemrow = mysqli_fetch_array($GetSystemData)) {
			$SystemUserName = stripslashes($Systemrow['firstName']);
			$SystemUserLastName = stripslashes($Systemrow['lastName']);
			$SystemUsername = stripslashes($Systemrow['username']);
			$Email = stripslashes($Systemrow['email']);

			$subject = $currenschoolDisplayname . "- Personnel CoARC Email Notification - " . $coarcSurveyTitle . " - " . $FullName;
			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/surveyDetails.html');

			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $SystemUserName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{URL}}' => $loginUrl,
				'{{SURVEY_NAME}}' => 'Personnel',
				'{{FULL_NAME}}' => $FullName,
				// '{{RANK}}' => $ranktitle,
				'{{TITLE}}' => 'Personnel CoARC Survey',
				'{{SURVEY_TYPE}}' => 'Clinician',
				'{{DISPLAY_RANK}}' => 'display:none;',
				'{{SURVEY_TITLE}}' => $coarcSurveyTitle,
				'Student Name :' => 'Clinician Name :',
				'{{DISPLAY_EMPLOYEE}}' => 'display:none;'
			);

			// Perform the replacements in one go using str_replace with arrays
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

			// if ($ranktitle == '') {
			// 	// Remove the entire Rank section from the HTML template
			// 	$pattern = '/<p\s*style="color:\s*#767676;\s*font-size:\s*16px;\s*font-style:\s*normal;\s*font-weight:\s*600;\s*padding:\s*0;\s*margin:\s*3px\s*0;">Rank\s*:\s*<span\s*style="color:\s*#000;">{{RANK}}<\/span><\/p>/i';
			// 	$emailContent = preg_replace($pattern, '', $emailContent);
			// }
			// print_r($emailContent);
			// exit;
			$this->SendEmail($subject, $emailContent, $Email);
		}
	}
	function SendNotificationToClinicianForIRRAssignment($studentId, $clinicianId, $currenschoolLogoImagePath, $currenschoolDisplayname, $schoolId)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select student.*,rankmaster.rankId,rankmaster.title
		From student 
		LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId
		Where studentId=" . $studentId;
		//echo 'sql->'.$studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$ranktitle = stripslashes($row['title']);

		$sql = "SELECT * FROM  clinician WHERE isActive='1'" . " AND schoolId=" . $schoolId . " AND clinicianId=" . $clinicianId;
		$GetClinicianData = $objDB->GetResultset($sql);

		while ($clinicianrow = mysqli_fetch_array($GetClinicianData)) {
			$ClinicianUserName = stripslashes($clinicianrow['firstName']);
			$ClinicianUserLastName = stripslashes($clinicianrow['lastName']);
			$ClinicianUsername = stripslashes($clinicianrow['username']);
			$Email = stripslashes($clinicianrow['email']);

			$subject = $currenschoolDisplayname . "-IRR Assignment Email Notification";
			$emailContent = $this->getEmailHeaderTable($currenschoolDisplayname, $currenschoolLogoImagePath);

			$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
						<tr>
						<td valign="top">
							<p>Hello, </strong> ' . $ClinicianUserName . '</p>
							<p>
							You received a IRR Assignment Notification Email.
							<br/><br/>
							IRR Assignment about below students.<br/>
							<strong>Student Name:</strong> ' . $FirstName . '<br/>			
							<strong>Rank:</strong> ' . $ranktitle . '<br/>			
							</p>
						</td>
						</tr>

						</table>';
			$emailContent .= $this->getEmailFooterTable();
			//print_r($emailContent);exit;	
			$this->SendEmail($subject, $emailContent, $Email);
		}
	}

	function SendTestCronJobEmail($Email = '<EMAIL>', $data = '')
	{

		$subject = "Clinical Trak Cron Job Started on: " . date('Y-m-d H:i:s');

		$emailContent = 'Cron Job has Started with data </br>';
		$emailContent .= '<p>DATA: </p>' . $data;

		$this->SendEmail($subject, $emailContent, $Email);
	}

	function SendEmailForAll($subject, $emailBody, $Email)
	{

		$loginUrl = BASE_PATH;

		$subject = $subject;
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/superAdminEmail.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{MESSAGE}}' => $emailBody,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;

		$ans = $this->SendEmail($subject, $emailContent, $Email);
		return $ans;
		// print_r($ans);exit;
	}










	// function SendEmailForAll($password,$studentId,$emailId,$currenschoolLogoImagePath,$currenschoolDisplayname)
	// {

	// 	//Read admin Details
	// 	$objDB = new clsDB();	
	// 	$sql = "Select * From student Where studentId=".$studentId;	

	// 	$row = $objDB->GetDataRow($sql);

	// 	$FirstName = stripslashes($row['firstName']);
	// 	$LastName = stripslashes($row['lastName']);
	// 	$Username = stripslashes($row['username']);
	// 	$Email = stripslashes($row['email']);

	// 	$subject = $currenschoolDisplayname."-Password  Notification";

	// 	$emailContent = $this->getEmailHeaderTable($currenschoolDisplayname,$currenschoolLogoImagePath);

	// 	$emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
	// 	<tr>
	// 	<td valign="top">
	// 		<p>Hello, </strong> '.$FirstName.'</p>

	// 		Below are your login details.<br/>
	// 		<strong>Username:</strong> '.$Username.'<br/>
	// 		<strong>Temporary Password:</strong> '.$password.'<br/>
	// 		</p>
	// 	</td>
	// 	</tr>

	// 	</table>';
	// 	$emailContent .= '<br/>You can click the URL below to login and continue</br>';
	// 	$emailContent.='<br/><a href="'.$this->currenschoolURL.'/student/index.html" >'.$this->currenschoolURL.'/student/index.html</a>';

	// 	$emailContent .= $this->getEmailFooterTable();
	// 		//print_r($emailContent);exit;	
	// 	$this->SendEmail($subject,$emailContent,$Email);

	// }
	function SendReviewMailToClinician($Email, $name, $studentName, $reviewDate, $currenschoolLogoImagePath = "", $currenschoolDisplayname = "")
	{

		//Read admin Details
		$objDB = new clsDB();
		$loginUrl = $this->currenschoolURL . '/clinician/index.html';
		$message = "Student $studentName has edited the Patient Assessment/Chart Review Case Study.";

		$subject = "Student Edited Patient Assessment/Chart Review Case Study";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/clinicianReview.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $name,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{MESSAGE}}' => $message,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Student Edited the PACR Case Study'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// $emailContent = $this->getEmailHeaderTableForReviewMailToClinician($currenschoolDisplayname, $currenschoolLogoImagePath);

		// $emailContent .= '<table width="100%" cellpadding="5" cellspacing="2" border="0">
		// <tr>
		// <td valign="top">
		// 	<p>Hello, </strong> ' . $name . '</p>

		// 	<p>Student ' . $studentName . '   has edited the Patient Assessment/Chart Review Case Study.
		// 	</p>
		// </td>
		// </tr>

		// </table>';
		// $emailContent .= '<br/>You can click the URL below to login and make the necessary updates</br>';
		// $emailContent .= '<br/><a href="' . $this->currenschoolURL . '/clinician/index.html" >' . $this->currenschoolURL . '/clinician/index.html</a>';

		// $emailContent .= $this->getEmailFooterTable();
		// print_r($emailContent);exit;	
		$this->SendEmail($subject, $emailContent, $Email);
	}

	function SendReviewMailToStudent($Email, $name, $studentName, $reviewDate, $currenschoolLogoImagePath = "", $currenschoolDisplayname = "")
	{

		//Read admin Details
		$objDB = new clsDB();
		$loginUrl = $this->currenschoolURL . '/student/index.html';
		$message = "Clinician $name has added a review to your Patient Assessment/Chart Review Case Study Dated $reviewDate.";
		$subject = "Clinician Added Review To Patient Assessment/Chart Review Case Study ";

		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/clinicianReview.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $studentName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{MESSAGE}}' => $message,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Clinician Added Review To PACR'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;	
		$this->SendEmail($subject, $emailContent, $Email);
	}

	/**
	 * Send email notification to preceptor to complete student's evaluation.
	 *
	 * @param string $Email Email address of preceptor.
	 * @param string $evalType Type of evaluation (e.g. dailyEval, activitySheet, etc.)
	 * @param string $link Link to evaluation form.
	 * @param string $preceptorCount Number of preceptors who have completed the evaluation.
	 * @param string $studentfullName Full name of student.
	 */
	function sendLinkToEmail($Email, $evalType, $link, $preceptorCount = '', $studentfullName = '')
	{

		$URL = $this->currenschoolURL . '/student/index.html';
		// $evalType = ($evalType == 'dailyEval') ? 'Daily/Weekly' : $evalType;
		if ($evalType == 'dailyEval') {
			$evalType = 'Daily/Weekly';
		} elseif ($evalType == 'activitySheet') {
			$evalType = 'Activity Sheet';
		}

		$studentName = ($studentfullName != '') ? ' from ' . $studentfullName : '';

		$subject = $this->currenschoolDisplayname . "- " . ucwords($evalType) . "  Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/sendLinkToEmail.html');
		if ($preceptorCount != '') {
			$message = 'You received a ' . ucwords($evalType) . ' email request' . $studentName . '.
						Please click the button below to finish the ' . $preceptorCount . ' completion  date and submit your ' . ucwords($evalType) . '.';
		} else {
			$message = 'You received a ' . ucwords($evalType) . ' email request' . $studentName . '.
						Please click the button below to complete the ' . ucwords($evalType) . '.';
		}

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{SURVEY_NAME}}' => 'Student',
			'{{URL}}' => $URL,
			'{{TITLE}}' => ucwords($evalType) . ' Request',
			'{{Link}}' => $link,
			'{{EVALUATION_TYPE}}' => ucwords($evalType),
			'{{MESSAGE}}' => $message,


		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		// $this->SendEmail($subject, $emailContent, '<EMAIL>');exit;
		$this->SendEmail($subject, $emailContent, $Email);
		// echo "send";
		// exit;

	}

	function SendCheckoffToStudentsByClinician($studentId, $selTopicId, $evaluationDate)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "SELECT * FROM student WHERE studentId =" . $studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$Email = stripslashes($row['email']);

		$sql = "SELECT *  FROM `schooltopicmaster` WHERE `schoolTopicId` =" . $selTopicId;
		$row = $objDB->GetDataRow($sql);
		$TopicName = stripslashes($row['schooltitle']);
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . "- Checkoff Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/checkoffEmail.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{URL}}' => $loginUrl,
			'{{TOPICNAME}}' => $TopicName,
			'{{DATE}}' => $evaluationDate

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);
		// exit;
		$this->SendEmail($subject, $emailContent, $Email);
	}

	function sendPreceptorEmailToStudent($Email, $studentFirstName, $externalPreceptorFullName, $completionLabel, $isCompletionCount, $topiTitle)
	{

		$URL = $this->currenschoolURL . '/student/index.html';
		// $evalType = ($evalType == 'dailyEval') ? 'Daily/Weekly' : $evalType;
		// $subject = $this->currenschoolDisplayname . "- " . ucwords($evalType) . " Email Notification";
		$subject = $this->currenschoolDisplayname . "- Checkoff Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/sendPreceptorEmailToStudent.html');
		if ($isCompletionCount == 5) {
			$message = 'All Preceptors has completed their sections. Please click the button below to complete the Checkoff.';
		} else {
			$message = 'Preceptor ' . $externalPreceptorFullName . ' has completed his section. Now you can send Checkoff to another Preceptor.';
		}
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{NAME}}' => $studentFirstName,
			'{{URL}}' => $URL,
			'{{TITLE}}' => 'Checkoff Completion Email',
			'{{MESSAGE}}' => $message,
			'{{Link}}' => $URL,
			'{{CHECKOFF_TOPIC}}' => $topiTitle


		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// print_r($emailContent);exit;
		// $this->SendEmail($subject, $emailContent, '<EMAIL>');exit;
		$this->SendEmail($subject, $emailContent, $Email);
		// echo "send";
		// exit;

	}
	// Send Credetntials to student for unlock by Admin
	function SendStudentUnlockDetails($studentId, $dynamicePassword)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "Select * From  student  Where studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . " - Account Unlocked Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Account Unlocked!',
			'{{MESSAGE}}' => 'Your account has been unlocked by School Admin. Below are your login details.'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
		$status = ($sendEmail == '202') ? 1 : 0;
	}

	function SendClinicianUnlockDetails($clinicianId, $dynamicePassword)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  clinician Where clinicianId=" . $clinicianId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/clinician/index.html';

		$subject = $this->currenschoolDisplayname . " - Account Unlocked Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Account Unlocked!',
			'{{MESSAGE}}' => 'Your account has been unlocked by School Admin. Below are your login details.'
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');
		$this->SendEmail($subject, $emailContent, $Email);
	}

	// Send Email for system user
	function SendSystemUserUnlockDetails($systemUserMasterId, $dynamicePassword)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/admin/index.html';

		$subject = $this->currenschoolDisplayname . " - Account Unlocked Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Account Unlocked!',
			'{{MESSAGE}}' => 'Your account has been unlocked by School Admin. Below are your login details.'
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
	}

	function SendEmailForAutoReport($reportId, $FirstName, $Email)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "SELECT adminreport.*, reportrepeatdaysmaster.title AS repeatTitle,rotation.title AS rotationName FROM `adminreport` 
		INNER JOIN reportrepeatdaysmaster ON reportrepeatdaysmaster.Id = adminreport.repeatId 
		INNER JOIN reportrepeatdaysdetails ON reportrepeatdaysdetails.detailId = adminreport.repeatOnId 
		LEFT JOIN rotation on rotation.rotationId  = adminreport.rotationId
		WHERE adminreport.reportId =" . $reportId;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$reportType = stripslashes($row['reportType']);
		$reportName = stripslashes($row['reportName']);
		$repeatTitle = stripslashes($row['repeatTitle']);
		$rotationName = isset($row['rotationName']) ? stripslashes($row['rotationName']) : '';
		$startDate = stripslashes($row['startDate']);
		$endDate = stripslashes($row['endDate']);


		$displayRotation = ($rotationName == '') ? 'display:none;' : '';
		// if ($reportType == 'DailyEval')
		// 	$reportType = 'Daily/Weekly';
		// else if ($reportType == 'Dr_Points')
		// 	$reportType = 'Dr. Interaction';
		// else if ($reportType == 'Procidure_Count')
		// 	$reportType = 'Procedure Count';
		// else if ($reportType == 'Late_clockIn')
		// 	$reportType = 'Late ClockIn';
		// else if ($reportType == 'Early_clockOut')
		// 	$reportType = 'Early ClockOut';
		// else if ($reportType == 'No_clockout')
		// 	$reportType = 'No Clockout';
		// else if ($reportType == 'Time_Exception')
		// 	$reportType = 'Time Exception';
		// else if ($reportType == 'Preceptor_Checkoff')
		// 	$reportType = 'Preceptor Checkoff';


		// $fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/admin/index.html';
		$URL = $this->currenschoolURL . '/admin/customReport.html?reportId=' . EncodeQueryData($reportId) . '&startDate=' . EncodeQueryData($startDate) . '&endDate=' . EncodeQueryData($endDate);
		// Create TinyUrl
		// $randomUrl = getTinyUrl($URL);

		// $redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
		// exit;
		$subject = $this->currenschoolDisplayname . " - " . $reportName . ' - Report';
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/autoReportEmail.html');

		$startDate = date("m/d/Y", strtotime($startDate));
		$endDate = date("m/d/Y", strtotime($endDate));

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{REPORT_TYPE}}' => $reportName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{ROTATION_NAME}}' => $rotationName,
			'{{URL}}' => $loginUrl,
			'{{Link}}' => $URL,
			'{{DATES}}' => $startDate . ' - ' . $endDate,
			'{{DISPLAY_ROTATION}}' => $displayRotation,
			'{{FREQUENCY}}' => $repeatTitle,

			'{{TITLE}}' => $reportName . ' Report',
			'{{MESSAGE}}' => 'Attached is the Scheduled Report, ' . $reportName . ' Report, for you to review and download. Should you have any questions or wish to change the frequency of this report please contact your DCE accordingly. Questions concerning the data contained within the report should be <NAME_EMAIL> or call ************.'
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $this->SendEmail($subject,$emailContent,'<EMAIL>');
		$this->SendEmail($subject, $emailContent, $Email);
	}

	function SendOTPForSelfActivateAccountToStudent($studentId, $otp, $isMobile = 0)
	{
		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From student Where studentId=" . $studentId;

		$row = $objDB->GetDataRow($sql);

		$phone = stripslashes($row['phone']);
		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$schoolId = stripslashes($row['schoolId']);
		$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $schoolId);

		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . "- Self Unlock Account OTP";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/SendOTP.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{PASSWORD}}' => $otp,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'OTP Received',
			'{{MESSAGE}}' => "You received an OTP to Self Unlock your account.<br> Your One Time Password(OTP) is : "

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		$this->SendEmail($subject, $emailContent, $Email);


		//Send sms 
		$body = "Hello $FirstName,\n\nYou received an OTP to Self Unlock your account. Your One Time Password(OTP) is : $otp  \n\nDo not share your OTP with anyone.";
		sendSMS('+' . $countryCode . $phone, $body);
	}


	function NotifyStudentUnlockedToStudent($studentId, $dynamicePassword)
	{
		//Read student Details
		$objDB = new clsDB();
		$sql = "Select * From  student  Where studentId=" . $studentId;
		$row = $objDB->GetDataRow($sql);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		$Email = stripslashes($row['email']);
		$studentfullName = $FirstName . ' ' . $LastName;
		$currentSchoolId = stripslashes($row['schoolId']);
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		$subject = $this->currenschoolDisplayname . " - Account Unlocked Email Notification";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/ChangePassword.html');

		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{NAME}}' => $FirstName,
			'{{FULL_NAME}}' => $studentfullName,
			'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{USERNAME}}' => $UserName,
			'{{PASSWORD}}' => $dynamicePassword,
			'{{URL}}' => $loginUrl,
			'{{TITLE}}' => 'Account Unlocked',
			'{{MESSAGE}}' => 'Your account is unlocked.<br> Below are your login credentials :'

		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		// $mailto:this->sendemail($subject,$emailcontent,'<EMAIL>');exit;
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);

		// $sql = "SELECT systemusermaster.firstName, systemusermaster.lastName, systemusermaster.email FROM systemuserrolemaster 
		// 		Inner join systemusermaster ON systemusermaster.systemUserRoleMasterId = systemuserrolemaster.systemUserRoleMasterId 
		// 		WHERE systemuserrolemaster.title IN ('D.C.E.', 'P.D.') AND systemusermaster.isActive = 1 AND systemuserrolemaster.schoolId = " . $currentSchoolId;
		// $GetSystemData = $objDB->GetResultset($sql);

		// while ($Systemrow = mysqli_fetch_array($GetSystemData)) {

		// 	$Name = stripslashes($Systemrow['firstName']);
		// 	$DCEEmail = stripslashes($Systemrow['email']);
		// 	$FirstName = stripslashes($row['firstName']);
		// 	$LastName = stripslashes($row['lastName']);
		// 	$UserName = stripslashes($row['username']);
		// 	$fullName = $FirstName . ' ' . $LastName;
		// 	$Email = stripslashes($row['email']);

		// 	$subject = $this->currenschoolDisplayname  . "- Student Account Unlocked Email Notification";

		// 	$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/sudentAccountUnlocked.html');

		// 	// Define the associative array with placeholders and their corresponding replacement values
		// 	$replaceValues = array(
		// 		'{{NAME}}' => $Name,
		// 		'{{FULL_NAME}}' => $fullName,
		// 		'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
		// 		'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
		// 		'{{USERNAME}}' => $UserName,
		// 		'{{EMAIL}}' => $Email,
		// 		'{{URL}}' => $loginUrl,
		// 		'{{TITLE}}' => 'Student Account Unlocked',
		// 		'{{MESSAGE}}' => $studentfullName . ' has self unlocked his account.'
		// 	);
		// 	$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);
		// 	// print_r($emailContent);
		// 	// exit;
		// 	$sendEmail = $this->SendEmail($subject, $emailContent, $DCEEmail);
		// }
		$status = ($sendEmail == '202') ? 1 : 0;
		unset($objDB);
	}

	/**
	 * SendCallOffNotification
	 *
	 * Sends an email notification to Admins or Clinicians when a student adds a call off
	 * 
	 * @param string $userIds Comma separated list of user Ids
	 * @param string $studentName Student's full name
	 * @param string $rotationName Rotation name
	 * @param string $userType Can be either 'Admin' or 'Clinician'
	 */
	function SendCallOffNotification($userIds, $studentName, $rotationName, $userType)
	{
		//Read admin Details
		$objDB = new clsDB();
		if ($userType == 'Admin') {
			$sql = "SELECT systemusermaster.firstName, systemusermaster.lastName, systemusermaster.email FROM systemusermaster  WHERE systemusermaster.systemUserMasterId IN (" . $userIds . ") AND systemusermaster.isActive = 1";
			// echo $sql;
			$GetUserData = $objDB->GetResultset($sql);
			$loginUrl = $this->currenschoolURL . '/admin/index.html';
		} else {
			$sql = "SELECT clinician.firstName, clinician.lastName, clinician.email FROM clinician  WHERE clinician.clinicianId IN (" . $userIds . ") AND clinician.isActive = 1";
			$GetUserData = $objDB->GetResultset($sql);
			$loginUrl = $this->currenschoolURL . '/clinician/index.html';
		}
		unset($objDB);
		while ($Systemrow = mysqli_fetch_array($GetUserData)) {
			$firstName = stripslashes($Systemrow['firstName']);
			$lastName = stripslashes($Systemrow['lastName']);
			$email = stripslashes($Systemrow['email']);

			$subject = $this->currenschoolDisplayname . "- Call Off Notification";
			$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/commonTemplate.html');
			$message = $studentName . ' student has added Call Off for the ' . $rotationName . ' rotation.';
			// Define the associative array with placeholders and their corresponding replacement values
			$replaceValues = array(
				'{{NAME}}' => $firstName,
				'{{SCHOOL_NAME}}' => $this->currenschoolDisplayname,
				'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
				'{{URL}}' => $loginUrl,
				'{{MESSAGE}}' => $message,
				'{{TITLE}}' => 'Call Off Notification',
				// '{{DISPLAY_BUTTON}}' => 'display:none;'
				'{{DISPLAY_BUTTON}}' => ''

			);

			// Perform the replacements in one go using str_replace with arrays
			$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

			// print_r($emailContent);
			// exit;
			$this->SendEmail($subject, $emailContent, $email);
		}
	}

	/* Sends immunization notification email to a student
	 * 
	 * @param string $email       Student's email address
	 * @param string $studentName Student's first name
	 * @param string $emailBody   The main content of the email
	 * @return void
	 */
	function SendImmunizationNotificationToStudent($email, $studentName, $emailBody)
	{
		// Initialize database connection
		$objDB = new clsDB();

		// Set email subject with school name
		$subject = $this->currenschoolDisplayname . " - Student  Immunization Expiry Reminder";

		// Load email template
		$emailContent = file_get_contents(__DIR__ . '/../includes/email_templates/commonTemplate.html');

		// Set login URL for student portal
		$loginUrl = $this->currenschoolURL . '/student/index.html';

		// Define template placeholders and their values
		$replaceValues = array(
			'{{NAME}}'           => $studentName,
			'{{SCHOOL_NAME}}'    => $this->currenschoolDisplayname,
			'{{SCHOOL_LOGO}}'    => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{URL}}'            => $loginUrl,
			'{{MESSAGE}}'        => $emailBody,
			'{{TITLE}}'          => 'Immunization Expiry Reminder',
			'{{DISPLAY_BUTTON}}' => 'display:none;',
		);

		// Replace placeholders with actual values
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);exit;

		// Send the email
		$this->SendEmail($subject, $emailContent, $email);
	}
	// Send Email for system user
	function SendSchoolStatusUpdateEmail($systemUserMasterId, $status, $schoolName)
	{

		//Read admin Details
		$objDB = new clsDB();
		$sql = "Select * From  systemusermaster Where systemUserMasterId=" . $systemUserMasterId;
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);

		$FirstName = stripslashes($row['firstName']);
		$LastName = stripslashes($row['lastName']);
		$UserName = stripslashes($row['username']);
		// $Email = stripslashes($row['email']);
		$fullName = $FirstName . ' ' . $LastName;
		$loginUrl = $this->currenschoolURL . '/admin/index.html';
		$subject = $schoolName . " - " . $status . "d School Account";
		$emailContent = file_get_contents(__DIR__  . '/../includes/email_templates/schoolStatusUpdate.html');
		// Define the associative array with placeholders and their corresponding replacement values
		$replaceValues = array(
			'{{FULL_NAME}}' => $fullName,
			'{{SCHOOL_NAME}}' => $schoolName,
			'{{STATUS}}' => $status,
			'{{SCHOOL_LOGO}}' => $this->currenschoolLogoImagePath . '?rand=' . rand(1, 100),
			'{{URL}}' => $loginUrl
		);

		// Perform the replacements in one go using str_replace with arrays
		$emailContent = str_replace(array_keys($replaceValues), array_values($replaceValues), $emailContent);

		// print_r($emailContent);
		// exit;
		$Email = '<EMAIL>';
		// $Email = '<EMAIL>';
		$sendEmail = $this->SendEmail($subject, $emailContent, $Email);
	}
}
