{"interfaces": {"google.cloud.secretmanager.v1.SecretManagerService": {"retry_codes": {"no_retry_codes": [], "no_retry_1_codes": [], "retry_policy_1_codes": ["UNAVAILABLE", "RESOURCE_EXHAUSTED"]}, "retry_params": {"no_retry_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 0, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 0, "total_timeout_millis": 0}, "no_retry_1_params": {"initial_retry_delay_millis": 0, "retry_delay_multiplier": 0.0, "max_retry_delay_millis": 0, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}, "retry_policy_1_params": {"initial_retry_delay_millis": 2000, "retry_delay_multiplier": 2.0, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1.0, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 60000}}, "methods": {"AccessSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "retry_policy_1_codes", "retry_params_name": "retry_policy_1_params"}, "AddSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "CreateSecret": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "DeleteSecret": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "DestroySecretVersion": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "DisableSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "EnableSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "GetIamPolicy": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "GetSecret": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "GetSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "ListSecretVersions": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "ListSecrets": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "SetIamPolicy": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "TestIamPermissions": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}, "UpdateSecret": {"timeout_millis": 60000, "retry_codes_name": "no_retry_1_codes", "retry_params_name": "no_retry_1_params"}}}}}