{"name": "google/protobuf", "type": "library", "description": "proto library for PHP", "keywords": ["proto"], "homepage": "https://developers.google.com/protocol-buffers/", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}}