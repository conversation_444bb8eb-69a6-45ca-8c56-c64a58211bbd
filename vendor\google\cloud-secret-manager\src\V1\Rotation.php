<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * The rotation time and period for a
 * [Secret][google.cloud.secretmanager.v1.Secret]. At next_rotation_time, Secret
 * Manager will send a Pub/Sub notification to the topics configured on the
 * Secret. [Secret.topics][google.cloud.secretmanager.v1.Secret.topics] must be
 * set to configure rotation.
 *
 * Generated from protobuf message <code>google.cloud.secretmanager.v1.Rotation</code>
 */
class Rotation extends \Google\Protobuf\Internal\Message
{
    /**
     * Optional. Timestamp in UTC at which the
     * [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to rotate.
     * Cannot be set to less than 300s (5 min) in the future and at most
     * 3153600000s (100 years).
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * MUST  be set if
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp next_rotation_time = 1 [(.google.api.field_behavior) = OPTIONAL];</code>
     */
    private $next_rotation_time = null;
    /**
     * Input only. The Duration between rotation notifications. Must be in seconds
     * and at least 3600s (1h) and at most 3153600000s (100 years).
     * If
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set,
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * must be set.
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * will be advanced by this period when the service automatically sends
     * rotation notifications.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration rotation_period = 2 [(.google.api.field_behavior) = INPUT_ONLY];</code>
     */
    private $rotation_period = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Protobuf\Timestamp $next_rotation_time
     *           Optional. Timestamp in UTC at which the
     *           [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to rotate.
     *           Cannot be set to less than 300s (5 min) in the future and at most
     *           3153600000s (100 years).
     *           [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     *           MUST  be set if
     *           [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     *           is set.
     *     @type \Google\Protobuf\Duration $rotation_period
     *           Input only. The Duration between rotation notifications. Must be in seconds
     *           and at least 3600s (1h) and at most 3153600000s (100 years).
     *           If
     *           [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     *           is set,
     *           [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     *           must be set.
     *           [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     *           will be advanced by this period when the service automatically sends
     *           rotation notifications.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Secretmanager\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * Optional. Timestamp in UTC at which the
     * [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to rotate.
     * Cannot be set to less than 300s (5 min) in the future and at most
     * 3153600000s (100 years).
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * MUST  be set if
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp next_rotation_time = 1 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @return \Google\Protobuf\Timestamp|null
     */
    public function getNextRotationTime()
    {
        return $this->next_rotation_time;
    }

    public function hasNextRotationTime()
    {
        return isset($this->next_rotation_time);
    }

    public function clearNextRotationTime()
    {
        unset($this->next_rotation_time);
    }

    /**
     * Optional. Timestamp in UTC at which the
     * [Secret][google.cloud.secretmanager.v1.Secret] is scheduled to rotate.
     * Cannot be set to less than 300s (5 min) in the future and at most
     * 3153600000s (100 years).
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * MUST  be set if
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp next_rotation_time = 1 [(.google.api.field_behavior) = OPTIONAL];</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setNextRotationTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->next_rotation_time = $var;

        return $this;
    }

    /**
     * Input only. The Duration between rotation notifications. Must be in seconds
     * and at least 3600s (1h) and at most 3153600000s (100 years).
     * If
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set,
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * must be set.
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * will be advanced by this period when the service automatically sends
     * rotation notifications.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration rotation_period = 2 [(.google.api.field_behavior) = INPUT_ONLY];</code>
     * @return \Google\Protobuf\Duration|null
     */
    public function getRotationPeriod()
    {
        return $this->rotation_period;
    }

    public function hasRotationPeriod()
    {
        return isset($this->rotation_period);
    }

    public function clearRotationPeriod()
    {
        unset($this->rotation_period);
    }

    /**
     * Input only. The Duration between rotation notifications. Must be in seconds
     * and at least 3600s (1h) and at most 3153600000s (100 years).
     * If
     * [rotation_period][google.cloud.secretmanager.v1.Rotation.rotation_period]
     * is set,
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * must be set.
     * [next_rotation_time][google.cloud.secretmanager.v1.Rotation.next_rotation_time]
     * will be advanced by this period when the service automatically sends
     * rotation notifications.
     *
     * Generated from protobuf field <code>.google.protobuf.Duration rotation_period = 2 [(.google.api.field_behavior) = INPUT_ONLY];</code>
     * @param \Google\Protobuf\Duration $var
     * @return $this
     */
    public function setRotationPeriod($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Duration::class);
        $this->rotation_period = $var;

        return $this;
    }

}

