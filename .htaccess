<IfModule mod_rewrite.c>


RewriteEngine On
RewriteCond %{THE_REQUEST} (.*)\.php  
RewriteRule ^(.*)\.php $1.html [R=301,L]

RewriteCond %{THE_REQUEST} (.*)\.html  
RewriteRule ^(.*)\.html $1.php [L]

RewriteCond %{REQUEST_URI} ^/(.*?/)?egypt(/.*)?$ [NC]
RewriteRule .* - [F,L]

#redirect Url Rules
RewriteRule ^redirect/([^/]*)/?$ redirect.php?redirectUrlId=$1 [NC,L]
RewriteRule ^redirect/([^/]*)/?$ redirect.php?redirectUrlId=$1 [NC,L]

# Admin Rules
RewriteRule ^school\/([^/]*)\/admin/index.php$ admin/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin$ admin/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/$ admin/index.php?schoolslug=$1 [QSA,L]


RewriteRule ^school\/([^/]*)\/admin/copyStudent.php$ admin/copyStudent.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/copyStudentSubmit.php$ admin/copyStudentSubmit.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/userLoginsubmit.php$ admin/userLoginsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianListForSuperAdmin.php$ admin/clinicianListForSuperAdmin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentListForSuperAdmin.php$ admin/studentListForSuperAdmin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/testCron.php$ admin/testCron.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/testCron1.php$ admin/testCron1.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/testCronAllData.php$ admin/testCronAllData.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dashboard.php$ admin/dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schools.php$ admin/schools.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolusertranssubmit.php$ admin/schoolusertranssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolusers.php$ admin/schoolusers.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolUserTransSubmit.php$ admin/schoolUserTransSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addschool.php$ admin/addschool.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addschoolsubmit.php$ admin/addschoolsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editschool.php$ admin/editschool.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editschoolsubmit.php$ admin/editschoolsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsystemuser.php$ admin/addsystemuser.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsystemusersubmit.php$ admin/addsystemusersubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/loginasschooluser.php$ admin/loginasschooluser.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/addclinician.php$ admin/addclinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcliniciansubmit.php$ admin/addcliniciansubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/singleclinicianimmunization.php$ admin/singleclinicianimmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianImmunizationDetails.php$ admin/clinicianImmunizationDetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addClinicianImmunization.php$ admin/addClinicianImmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addClinicianImmunizationsubmit.php$ admin/addClinicianImmunizationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editclinicianImmunization.php$ admin/editclinicianImmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editclinicianimmunizationsubmit.php$ admin/editclinicianimmunizationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportClinicianImmuList.php$ admin/exportClinicianImmuList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewsystemuserroles.php$ admin/viewsystemuserroles.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsystemuserrole.php$ admin/addsystemuserrole.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsystemuserrolesubmit.php$ admin/addsystemuserrolesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/courses.php$ admin/courses.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcourse.php$ admin/addcourse.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcoursesubmit.php$ admin/addcoursesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usersLog.php$ admin/usersLog.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clearalllog.php$ admin/clearalllog.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adminattendance.php$ admin/adminattendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/attendancesubmit.php$ admin/attendancesubmit.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/settings.php$ admin/settings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/personnelCoarcSurveyReport.php$ admin/personnelCoarcSurveyReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportGraduateServey.php$ admin/exportGraduateServey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportPersonnelServey.php$ admin/exportPersonnelServey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentServey.php$ admin/exportStudentServey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/personnelCoarcSurveyReportView.php$ admin/personnelCoarcSurveyReportView.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolclinicians.php$ admin/schoolclinicians.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolclinicianstransubmit.php$ admin/schoolclinicianstransubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianCertificationLog.php$ admin/clinicianCertificationLog.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/ClinicianCertificationLogSubmit.php$ admin/ClinicianCertificationLogSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianDocuments.php$ admin/clinicianDocuments.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianDocumentUpload.php$ admin/clinicianDocumentUpload.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cliniciandocumentUploadSubmit.php$ admin/cliniciandocumentUploadSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianCoarclist.php$ admin/clinicianCoarclist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolStudents.php$ admin/schoolStudents.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolStudentsTransSubmit.php$ admin/schoolStudentsTransSubmit.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/logout.php$ admin/logout.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/forgotpassword.php$ admin/forgotpassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editprofile.php$ admin/editprofile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/faqquestion.php$ admin/faqquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editprofilesubmit.php$ admin/editprofilesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/changepassword.php$ admin/changepassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/forgotpasswordsubmit.php$ admin/forgotpasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/changepasswordsubmit.php$ admin/changepasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/smtpsettings.php$ admin/smtpsettings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/smtpsettingsubmit.php$ admin/smtpsettingsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/casestudySettings.php$ admin/casestudySettings.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/loginasschoolstudent.php$ admin/loginasschoolstudent.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/loginasschoolclinicians.php$ admin/loginasschoolclinicians.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/displayschoolsystemuser.php$ admin/displayschoolsystemuser.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolTransSubmit.php$ admin/schoolTransSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/displayschoolsystemusertranssubmit.php$ admin/displayschoolsystemusertranssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addlocation.php$ admin/addlocation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addlocationsubmit.php$ admin/addlocationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoollocation.php$ admin/schoollocation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addhospitalsites.php$ admin/addhospitalsites.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addhospitalsitessubmit.php$ admin/addhospitalsitessubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/hospitalsites.php$ admin/hospitalsites.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rotations.php$ admin/rotations.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addrotations.php$ admin/addrotations.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addrotationsubmit.php$ admin/addrotationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assignstudents.php$ admin/assignstudents.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/lockscreen.php$ admin/lockscreen.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/lockscreensubmit.php$ admin/lockscreensubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsubrotation.php$ admin/addsubrotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentSchedule.php$ admin/studentSchedule.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentRotationAssignList.php$ admin/exportStudentRotationAssignList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assignstudentsList.php$ admin/assignstudentsList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/ExcelExport.php$ admin/ExcelExport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exporttoexcel.php$ admin/exporttoexcel.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addranking.php$ admin/addranking.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addrankingsubmit.php$ admin/addrankingsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewranking.php$ admin/viewranking.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addclinicianroles.php$ admin/addclinicianroles.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addclinicianrolesubmit.php$ admin/addclinicianrolesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewclinicianroles.php$ admin/viewclinicianroles.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/reports.php$ admin/reports.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/reportsubmit.php$ admin/reportsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentDetailReport.php$ admin/studentDetailReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journal.php$ admin/journal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journalsubmit.php$ admin/journalsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addjournal.php$ admin/addjournal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoff.php$ admin/checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffs.php$ admin/checkoffs.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcheckoff.php$ admin/addcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcheckoffssubmit.php$ admin/addcheckoffssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentTopicList.php$ admin/studentTopicList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/reports.php$ admin/reports.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/reportsubmit.php$ admin/reportsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journal.php$ admin/journal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journalsubmit.php$ admin/journalsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addjournal.php$ admin/addjournal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/get_rotation.php$ admin/get_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/immunization.php$ admin/immunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addimmunization.php$ admin/addimmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addImmunizationsubmit.php$ admin/addImmunizationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentimmunization.php$ admin/studentimmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentImmuList.php$ admin/exportStudentImmuList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editstudentimmunization.php$ admin/editstudentimmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editimmunizationsubmit.php$ admin/editimmunizationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentpromotion.php$ admin/studentpromotion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentpromotionSubmit.php$ admin/studentpromotionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/singlestudentimmunization.php$ admin/singlestudentimmunization.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addimmunizationmaster.php$ admin/addimmunizationmaster.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addImmunizationmastersubmit.php$ admin/addImmunizationmastersubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formative.php$ admin/formative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/get_clinician.php$ admin/get_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativesubmit.php$ admin/formativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativelist.php$ admin/formativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summative.php$ admin/summative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativesubmit.php$ admin/summativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativelist.php$ admin/summativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/accreditation.php$ admin/accreditation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/accreditationsubmit.php$ admin/accreditationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/CertificationLog.php$ admin/CertificationLog.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/CertificationLogSubmit.php$ admin/CertificationLogSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/AdditationlContactInformation.php$ admin/AdditationlContactInformation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/AdditationlContactInformationSubmit.php$ admin/AdditationlContactInformationSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cievaluationsubmit.php$ admin/cievaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cievaluation.php$ admin/cievaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cievaluationlist.php$ admin/cievaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicalsiteunit.php$ admin/clinicalsiteunit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addclinicalsiteunit.php$ admin/addclinicalsiteunit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addclinicalsiteunitsubmit.php$ admin/addclinicalsiteunitsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteevaluationsubmit.php$ admin/siteevaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteevaluation.php$ admin/siteevaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteevaluationlist.php$ admin/siteevaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/interaction.php$ admin/interaction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addinteraction.php$ admin/addinteraction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/interactionsubmit.php$ admin/interactionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/equipmentsubmit.php$ admin/equipmentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/equipment.php$ admin/equipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/equipmentlist.php$ admin/equipmentlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journallist.php$ admin/journallist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinical.php$ admin/clinical.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rotationhistory.php$ admin/rotationhistory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentDocuments.php$ admin/studentDocuments.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/documentUpload.php$ admin/documentUpload.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/documentUploadSubmit.php$ admin/documentUploadSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewDocuments.php$ admin/viewDocuments.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/downloadDocument.php$ admin/downloadDocument.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcsurvey.php$ admin/coarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcsurveysubmit.php$ admin/coarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcoarcsurvey.php$ admin/addcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcoarcsurveysubmit.php$ admin/addcoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoff.php$ admin/checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentCheckoffList.php$ admin/studentCheckoffList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcheckoffsubmit.php$ admin/addcheckoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/graduatecoarcsurvey.php$ admin/graduatecoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewgraduatecoarcsurvey.php$ admin/viewgraduatecoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/employercoarcsurvey.php$ admin/employercoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/employeeCoarcSurveyReport.php$ admin/employeeCoarcSurveyReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/employeerCoarcSurveyReportView.php$ admin/employeerCoarcSurveyReportView.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addemployercoarcsurvey.php$ admin/addemployercoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sendemployercoarcrequest.php$ admin/sendemployercoarcrequest.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentCoarcSurveyReport.php$ admin/studentCoarcSurveyReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentCoarcSurveyReportView.php$ admin/studentCoarcSurveyReportView.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecounts.php$ admin/procedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addprocedurecountsubmit.php$ admin/addprocedurecountsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/employercoarcsurveysubmit.php$ admin/employercoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addemployercoarcsurveysubmit.php$ admin/addemployercoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcSurveyReport.php$ admin/coarcSurveyReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcSurveyReportView.php$ admin/coarcSurveyReportView.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/personnelrequest.php$ admin/personnelrequest.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addpersonnelcoarcsurvey.php$ admin/addpersonnelcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/personnelcoarcrequestsubmit.php$ admin/personnelcoarcrequestsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addpersonnelsurvey.php$ admin/addpersonnelsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addpersonnelsurveysubmit.php$ admin/addpersonnelsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addirr.php$ admin/addirr.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irrsubmit.php$ admin/irrsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irr.php$ admin/irr.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianlist.php$ admin/clinicianlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportreport.php$ admin/exportreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewsemester.php$ admin/viewsemester.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsemester.php$ admin/addsemester.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/semestersubmit.php$ admin/semestersubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcheckofftopics.php$ admin/addcheckofftopics.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkofftopicsubmit.php$ admin/checkofftopicsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkofftopics.php$ admin/checkofftopics.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffsection.php$ admin/checkoffsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsection.php$ admin/addsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffsectionsubmit.php$ admin/checkoffsectionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/questions.php$ admin/questions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rotationlist.php$ admin/rotationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportschoolreport.php$ admin/exportschoolreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addquestion.php$ admin/addquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/questionsubmit.php$ admin/questionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/incident.php$ admin/incident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addincident.php$ admin/addincident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addincidentsubmit.php$ admin/addincidentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/questiondetails.php$ admin/questiondetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assign_sections.php$ admin/assign_sections.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewquestions.php$ admin/viewquestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/editquestion.php$ admin/editquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/questionupdatesubmit.php$ admin/questionupdatesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecotegory.php$ admin/procedurecotegory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addcotegory.php$ admin/addcotegory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cotegorysubmit.php$ admin/cotegorysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/notification.php$ admin/notification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/notificationsubmit.php$ admin/notificationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/shownotification.php$ admin/shownotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adminnotification.php$ admin/adminnotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffstatus.php$ admin/checkoffstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addprocedurecounts.php$ admin/addprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/submitprocedurecounts.php$ admin/submitprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewprocedurecounts.php$ admin/viewprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rechangepassword.php$ admin/rechangepassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rechnagepasswordsubmit.php$ admin/rechnagepasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rechangestudentpassword.php$ admin/rechangestudentpassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/rechangestudentpasswordsubmit.php$ admin/rechangestudentpasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_copyschooldefaultdata.php$ admin/cron_copyschooldefaultdata.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/test.php$ admin/test.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_deleteschooldefaultdata.php$ admin/cron_deleteschooldefaultdata.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/mastercheckofftopic.php$ admin/mastercheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteraddcheckofftopic.php$ admin/masteraddcheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/mastercheckofftopicsubmit.php$ admin/mastercheckofftopicsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/mastercheckoffsection.php$ admin/mastercheckoffsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/mastercheckoffsectionsubmit.php$ admin/mastercheckoffsectionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteraddsection.php$ admin/masteraddsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterquestion.php$ admin/masterquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteraddquestion.php$ admin/masteraddquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterquestionsubmit.php$ admin/masterquestionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterquestiondetails.php$ admin/masterquestiondetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterquestiondetails.php$ admin/advancemasterquestiondetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterviewquestions.php$ admin/usafmasterviewquestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterviewsection.php$ admin/usafmasterviewsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasteraddsection.php$ admin/usafmasteraddsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmastercheckoffsectionsubmit.php$ admin/usafmastercheckoffsectionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewusafprocedurecounts.php$ admin/viewusafprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addusafprocedurecounts.php$ admin/addusafprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addusafprocedurecountssubmit.php$ admin/addusafprocedurecountssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafprocedurecotegory.php$ admin/usafprocedurecotegory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addusafcategory.php$ admin/addusafcategory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addusafcategorysubmit.php$ admin/addusafcategorysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmastercheckofftopic.php$ admin/usafmastercheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasteraddcheckofftopic.php$ admin/usafmasteraddcheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterusafcheckofftopicsubmit.php$ admin/masterusafcheckofftopicsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmastercheckoffsection.php$ admin/usafmastercheckoffsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterquestion.php$ admin/usafmasterquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasteraddquestion.php$ admin/usafmasteraddquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterquestionsubmit.php$ admin/usafmasterquestionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmastereditquestion.php$ admin/usafmastereditquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterquestionupdatesubmit.php$ admin/usafmasterquestionupdatesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEvaluationSectionList.php$ admin/dailyEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addDailyEvaluationSection.php$ admin/addDailyEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailyEvaluationSectionSubmit.php$ admin/adddailyEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEvaluationQuestionList.php$ admin/dailyEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailyEvaluationQuestions.php$ admin/adddailyEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailyEvaluationQuestionSubmit.php$ admin/adddailyEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sendEmailRecords.php$ admin/sendEmailRecords.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sendAnnouncementRecords.php$ admin/sendAnnouncementRecords.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/announcementList.php$ admin/announcementList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/countryCode.php$ admin/countryCode.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/callOff.php$ admin/callOff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/callOffList.php$ admin/callOffList.php?schoolslug=$1 [QSA,L]


RewriteRule ^school\/([^/]*)\/admin/dailySchoolEvaluationSectionList.php$ admin/dailySchoolEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addDailySchoolEvaluationSection.php$ admin/addDailySchoolEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailySchoolEvaluationSectionSubmit.php$ admin/adddailySchoolEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailySchoolEvaluationQuestionList.php$ admin/dailySchoolEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailySchoolEvaluationQuestions.php$ admin/adddailySchoolEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adddailySchoolEvaluationQuestionSubmit.php$ admin/adddailySchoolEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterquestiondetails.php$ admin/usafmasterquestiondetails.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/masterviewquestions.php$ admin/masterviewquestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/mastereditquestion.php$ admin/mastereditquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterquestionupdatesubmit.php$ admin/masterquestionupdatesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masterviewsection.php$ admin/masterviewsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sectionupdate.php$ admin/sectionupdate.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewsectionmaster.php$ admin/viewsectionmaster.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsectionmaster.php$ admin/addsectionmaster.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sectionviewsubmit.php$ admin/sectionviewsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sectionmastersubmit.php$ admin/sectionmastersubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/map.php$ admin/map.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/Attendance.php$ admin/Attendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/attendancereport.php$ admin/attendancereport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianattendancereport.php$ admin/clinicianattendancereport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentreport.php$ admin/studentreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativereport.php$ admin/formativereport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativereport.php$ admin/summativereport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cievalreport.php$ admin/cievalreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/Midtermreport.php$ admin/Midtermreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteevalreport.php$ admin/siteevalreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/equipmentreport.php$ admin/equipmentreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/drinteractionreport.php$ admin/drinteractionreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffreport.php$ admin/checkoffreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/timeexceptionreport.php$ admin/timeexceptionreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecountdetailsreport.php$ admin/procedurecountdetailsreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecountreport.php$ admin/procedurecountreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journalreport.php$ admin/journalreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irrreport.php$ admin/irrreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportirrReport.php$ admin/exportirrReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentdetailsreport.php$ admin/studentdetailsreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irrdetailsreport.php$ admin/irrdetailsreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativedetailreport.php$ admin/summativedetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativedetailreport.php$ admin/formativedetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/journaldetailsreport.php$ admin/journaldetailsreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/attendancedetailsreport.php$ admin/attendancedetailsreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEvalreport.php$ admin/dailyEvalreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteevaldetailreport.php$ admin/siteevaldetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/equipmentdetailreport.php$ admin/equipmentdetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/drinteractiondetailreport.php$ admin/drinteractiondetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffdetailreport.php$ admin/checkoffdetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/timeexceptiondetailreport.php$ admin/timeexceptiondetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecountdetailreport.php$ admin/procedurecountdetailreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_copyremainingdefaultdata.php$ admin/cron_copyremainingdefaultdata.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/copyrotations.php$ admin/copyrotations.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/copyrotationsubmit.php$ admin/copyrotationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/midterm.php$ admin/midterm.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/midtermlist.php$ admin/midtermlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/midtermsubmit.php$ admin/midtermsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron.php$ admin/cron.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_send_email.php$ admin/cron_send_email.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irrreportview.php$ admin/irrreportview.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/irrreport.php$ admin/irrreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/immunizationdetails.php$ admin/immunizationdetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcSurveylist.php$ admin/coarcSurveylist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assigncoarcsurvey.php$ admin/assigncoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assignsurveysubmit.php$ admin/assignsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentlist.php$ admin/studentlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecountsNew.php$ admin/procedurecountsNew.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterviewquestions.php$ admin/advancemasterviewquestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemastereditquestion.php$ admin/advancemastereditquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterquestionupdatesubmit.php$ admin/advancemasterquestionupdatesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemastercheckofftopic.php$ admin/advancemastercheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterviewsection.php$ admin/advancemasterviewsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasteraddcheckofftopic.php$ admin/advancemasteraddcheckofftopic.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemastercheckofftopicsubmit.php$ admin/advancemastercheckofftopicsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemastercheckoffsection.php$ admin/advancemastercheckoffsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasteraddsection.php$ admin/advancemasteraddsection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemastercheckoffsectionsubmit.php$ admin/advancemastercheckoffsectionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterquestion.php$ admin/advancemasterquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasteraddquestion.php$ admin/advancemasteraddquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advanceviewprocedurecounts.php$ admin/advanceviewprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadvancecotegory.php$ admin/addadvancecotegory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadvancecategorysubmit.php$ admin/addadvancecategorysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advanceprocedurecotegory.php$ admin/advanceprocedurecotegory.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadvanceprocedurecounts.php$ admin/addadvanceprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadvanceprocedurecountsubmit.php$ admin/addadvanceprocedurecountsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkofftopicstatus.php$ admin/checkofftopicstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentDrInteractionReport.php$ admin/studentDrInteractionReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentClinicalSiteUnitReport.php$ admin/studentClinicalSiteUnitReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentSummativeEvaluationReport.php$ admin/studentSummativeEvaluationReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentFormativeEvaluationReport.php$ admin/studentFormativeEvaluationReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentProcedureCountReport.php$ admin/studentProcedureCountReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentAttendanceReport.php$ admin/studentAttendanceReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentCheckoffReport.php$ admin/studentCheckoffReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentMidtermReport.php$ admin/studentMidtermReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentDetails.php$ admin/exportStudentDetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoff-grid-data.php$ admin/checkoff-grid-data.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoff-report-data.php$ admin/checkoff-report-data.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/advancemasterquestionsubmit.php$ admin/advancemasterquestionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEvalList.php$ admin/dailyEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEvalSubmit.php$ admin/dailyEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/dailyEval.php$ admin/dailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/getAllhospitalForDailyEval.php$ admin/getAllhospitalForDailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/checkoffusaf.php$ admin/checkoffusaf.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addusafcheckoff.php$ admin/addusafcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasterviewequipment.php$ admin/usafmasterviewequipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasteraddequipment.php$ admin/usafmasteraddequipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafmasteraddequipmentsubmit.php$ admin/usafmasteraddequipmentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafschoolviewequipment.php$ admin/usafschoolviewequipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafschooladdequipment.php$ admin/usafschooladdequipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/usafschooladdequipmentsubmit.php$ admin/usafschooladdequipmentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/volunteerEvalList.php$ admin/volunteerEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addVolunteerEval.php$ admin/addVolunteerEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addVolunteerEvalSubmit.php$ admin/addVolunteerEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/caseStudyList.php$ admin/caseStudyList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudy.php$ admin/addCaseStudy.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudyAdult.php$ admin/addCaseStudyAdult.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudyAdultSubmit.php$ admin/addCaseStudyAdultSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudySubmit.php$ admin/addCaseStudySubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/showTopicStepsInModel.php$ admin/showTopicStepsInModel.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/ciEvaluationSectionList.php$ admin/ciEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/ciEvaluationQuestionList.php$ admin/ciEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addciEvaluationSection.php$ admin/addciEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addciEvaluationSectionSubmit.php$ admin/addciEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addciEvaluationQuestions.php$ admin/addciEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addciEvaluationQuestionSubmit.php$ admin/addciEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewMedicalTerminology.php$ admin/viewMedicalTerminology.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMedicalTerminology.php$ admin/addMedicalTerminology.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMedicalTerminologySubmit.php$ admin/addMedicalTerminologySubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewMedicalTerminologyFiles.php$ admin/viewMedicalTerminologyFiles.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMedicalTerminologyFile.php$ admin/addMedicalTerminologyFile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMedicalTerminologyFileSubmit.php$ admin/addMedicalTerminologyFileSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMedicalTerminologyFileSubmit.php$ admin/addMedicalTerminologyFileSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assign_medical_terminology_status.php$ admin/assign_medical_terminology_status.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentJournalEntryCount.php$ admin/studentJournalEntryCount.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentJournalEntryCountSubmit.php$ admin/studentJournalEntryCountSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentInteractionEntryCount.php$ admin/studentInteractionEntryCount.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentInteractionEntryCountSubmit.php$ admin/studentInteractionEntryCountSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewStudentEmail.php$ admin/viewStudentEmail.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteEvaluationSectionList.php$ admin/siteEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsiteEvaluationSection.php$ admin/addsiteEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsiteEvaluationSectionSubmit.php$ admin/addsiteEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/siteEvaluationQuestionList.php$ admin/siteEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsiteEvaluationQuestions.php$ admin/addsiteEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsiteEvaluationQuestionSubmit.php$ admin/addsiteEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativeEvaluationSectionList.php$ admin/formativeEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addFormativeEvaluationSection.php$ admin/addFormativeEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addformativeEvaluationSectionSubmit.php$ admin/addformativeEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/formativeEvaluationQuestionList.php$ admin/formativeEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addformativeEvaluationQuestions.php$ admin/addformativeEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addformativeEvaluationQuestionSubmit.php$ admin/addformativeEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/midtermEvaluationSectionList.php$ admin/midtermEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmidtermEvaluationSection.php$ admin/addmidtermEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmidtermEvaluationSectionSubmit.php$ admin/addmidtermEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/midtermEvaluationQuestionList.php$ admin/midtermEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmidtermEvaluationQuestions.php$ admin/addmidtermEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmidtermEvaluationQuestionSubmit.php$ admin/addmidtermEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativeEvaluationSectionList.php$ admin/summativeEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsummativeEvaluationSection.php$ admin/addsummativeEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsummativeEvaluationSectionSubmit.php$ admin/addsummativeEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/summativeEvaluationQuestionList.php$ admin/summativeEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsummativeEvaluationQuestions.php$ admin/addsummativeEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addsummativeEvaluationQuestionSubmit.php$ admin/addsummativeEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importstudentlist.php$ admin/importstudentlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importstudent.php$ admin/importstudent.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/updateallevaluationsectionstatus.php$ admin/updateallevaluationsectionstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/superadminnotification.php$ admin/superadminnotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteryList.php$ admin/masteryList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addMastery.php$ admin/addMastery.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteryEvalSubmit.php$ admin/masteryEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolcliniciansshedule.php$ admin/schoolcliniciansshedule.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolholidays.php$ admin/schoolholidays.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/calendar.php$ admin/calendar.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addschoolholiday.php$ admin/addschoolholiday.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolholidaysubmit.php$ admin/schoolholidaysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/hospitalSiteCalendar.php$ admin/hospitalSiteCalendar.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentListOfDocumentUploaded.php$ admin/studentListOfDocumentUploaded.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentRegister.php$ admin/studentRegister.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/thankyou.php$ admin/thankyou.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addRegistrationNotification.php$ admin/addRegistrationNotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentIdsList.php$ admin/studentIdsList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importStudentIdslist.php$ admin/importStudentIdslist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportstudentIds.php$ admin/exportstudentIds.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/assignCavasToSchools.php$ admin/assignCavasToSchools.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/programDatesList.php$ admin/programDatesList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importProgramDatesList.php$ admin/importProgramDatesList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentProgramDates.php$ admin/exportStudentProgramDates.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sendAbsenceSMS.php$ admin/sendAbsenceSMS.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/sendCanvasRequestToStudent.php$ admin/sendCanvasRequestToStudent.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importCheckoffSection.php$ admin/importCheckoffSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importSuperadminCheckoff.php$ admin/importSuperadminCheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importSuperadminCheckoffSubmit.php$ admin/importSuperadminCheckoffSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pefList.php$ admin/pefList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEFEvaluation.php$ admin/addPEFEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEFEvaluationsubmit.php$ admin/addPEFEvaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pevaluationlist.php$ admin/pevaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEvaluation.php$ admin/addPEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addpevaluationsubmit.php$ admin/addpevaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/floorTherapyAndICUEvaluationlist.php$ admin/floorTherapyAndICUEvaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addFloorTherapyAndICUEvaluation.php$ admin/addFloorTherapyAndICUEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/evaluationList.php$ admin/evaluationList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importEvaluationList.php$ admin/importEvaluationList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importEvaluationListSection.php$ admin/importEvaluationListSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addFloorTherapyAndICUEvaluationSubmit.php$ admin/addFloorTherapyAndICUEvaluationSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportEmployerServey.php$ admin/exportEmployerServey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/email.php$ admin/email.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/emailReport.php$ admin/emailReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewEmailReport.php$ admin/viewEmailReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pouch.php$ admin/pouch.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pouchUpload.php$ admin/pouchUpload.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pouchUploadSubmit.php$ admin/pouchUploadSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudyPACR.php$ admin/addCaseStudyPACR.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCaseStudyPACRSubmit.php$ admin/addCaseStudyPACRSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addAnnouncement.php$ admin/addAnnouncement.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentScheduleList.php$ admin/exportStudentScheduleList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/preceptorDetails.php$ admin/preceptorDetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/importAdvanceCheckoffSection.php$ admin/importAdvanceCheckoffSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_advancecheckoffimport.php$ admin/cron_advancecheckoffimport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_updateHours.php$ admin/cron_updateHours.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/removeSections.php$ admin/removeSections.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/coarcSurveypdf.php$ admin/coarcSurveypdf.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/autoReport.php$ admin/autoReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/autoreportsubmit.php$ admin/autoreportsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adminreportlist.php$ admin/adminreportlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/pdfViewForAllEvaluations.php$ admin/pdfViewForAllEvaluations.php?schoolslug=$1 [QSA,L]

#Mastery Evaluation
RewriteRule ^school\/([^/]*)\/admin/masteryEvaluationSectionList.php$ admin/masteryEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmasteryEvaluationSection.php$ admin/addmasteryEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmasteryEvaluationSectionSubmit.php$ admin/addmasteryEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/masteryEvaluationQuestionList.php$ admin/masteryEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmasteryEvaluationQuestions.php$ admin/addmasteryEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addmasteryEvaluationQuestionSubmit.php$ admin/addmasteryEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]


#User Details from dashboard
RewriteRule ^school\/([^/]*)\/admin/userDetails.php$ admin/userDetails.php?schoolslug=$1 [QSA,L]

#For Admin CI Evaluation
RewriteRule ^school\/([^/]*)\/admin/admincievaluationlist.php$ admin/admincievaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/admincievaluationsubmit.php$ admin/admincievaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/admincievaluation.php$ admin/admincievaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adminciEvaluationSectionList.php$ admin/adminciEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadminciEvaluationSection.php$ admin/addadminciEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadminciEvaluationSectionSubmit.php$ admin/addadminciEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/adminciEvaluationQuestionList.php$ admin/adminciEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadminciEvaluationQuestions.php$ admin/addadminciEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addadminciEvaluationQuestionSubmit.php$ admin/addadminciEvaluationQuestionSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/activitysheetlist.php$ admin/activitysheetlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/studentListForAdmin.php$ admin/studentListForAdmin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/activitySheet.php$ admin/activitySheet.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/activitysheetsubmit.php$ admin/activitysheetsubmit.php?schoolslug=$1 [QSA,L]


# For Admin P Evaluation
RewriteRule ^school\/([^/]*)\/admin/PEvaluationSectionList.php$ admin/PEvaluationSectionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEvaluationSection.php$ admin/addPEvaluationSection.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEvaluationSectionSubmit.php$ admin/addPEvaluationSectionSubmit.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/pEvaluationQuestionList.php$ admin/pEvaluationQuestionList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEvaluationQuestions.php$ admin/addPEvaluationQuestions.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addPEvaluationQuestionsSubmit.php$ admin/addPEvaluationQuestionsSubmit.php?schoolslug=$1 [QSA,L]

#Soap Note for Admin
RewriteRule ^school\/([^/]*)\/admin/soapNoteList.php$ admin/soapNoteList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addSoapNote.php$ admin/addSoapNote.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/soapNoteSubmit.php$ admin/soapNoteSubmit.php?schoolslug=$1 [QSA,L]
#Additional Settings 
RewriteRule ^school\/([^/]*)\/admin/additionalsettings.php$ admin/additionalsettings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/additionalsettingsubmit.php$ admin/additionalsettingsubmit.php?schoolslug=$1 [QSA,L]


#For graduate student list on dashboard
RewriteRule ^school\/([^/]*)\/admin/graduatestudentlist.php$ admin/graduatestudentlist.php?schoolslug=$1 [QSA,L]


#For Clock Out Notification 
RewriteRule ^school\/([^/]*)\/admin/clockoutNotification.php$ admin/clockoutNotification.php?schoolslug=$1 [QSA,L]
#For Call Off Notification
RewriteRule ^school\/([^/]*)\/admin/callOffNotification.php$ admin/callOffNotification.php?schoolslug=$1 [QSA,L]


# service Rules
RewriteRule ^school\/([^/]*)\/service/TEST.php$ service/TEST.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/service/forgotPassword.php$ service/forgotPassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/service/authenticate.php$ service/authenticate.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/service/getUserDetails.php$ service/getUserDetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/service/getAllRotations.php$ service/getAllRotations.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/customReport.php$ admin/customReport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/cron_autoReport.php$ admin/cron_autoReport.php?schoolslug=$1 [QSA,L]

#Mobile app Release note
RewriteRule ^school\/([^/]*)\/admin/appReleaseNote.php$ admin/appReleaseNote.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/appReleaseNoteSubmit.php$ admin/appReleaseNoteSubmit.php?schoolslug=$1 [QSA,L]

#Call Off
RewriteRule ^school\/([^/]*)\/admin/callOffList.php$ admin/callOffList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addCallOff.php$ admin/addCallOff.php?schoolslug=$1 [QSA,L]


# Student Rules

RewriteRule ^school\/([^/]*)\/admin/addstudent.php$ admin/addstudent.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addstudentsubmit.php$ admin/addstudentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolstudentstranssubmit.php$ admin/schoolstudentstranssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/schoolstudents.php$ admin/schoolstudents.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/student/index.php$ student/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student$ student/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/$ student/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/forgotpassword.php$ student/forgotpassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/forgotpasswordsubmit.php$ student/forgotpasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentLoginsubmit.php$ student/studentLoginsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/employerLoginsubmit.php$ student/employerLoginsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/dashboard.php$ student/dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/dashboardnew.php$ student/dashboardnew.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/logout.php$ student/logout.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/editprofile.php$ student/editprofile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/faqquestion.php$ student/faqquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/editprofilesubmit.php$ student/editprofilesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/changepassword.php$ student/changepassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/changepasswordsubmit.php$ student/changepasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/courses.php$ student/courses.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/rotations.php$ student/rotations.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentrotation.php$ student/studentrotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/clockin.php$ student/clockin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/clockinsubmit.php$ student/clockinsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/clockintranssubmit.php$ student/clockintranssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentAttendance.php$ student/studentAttendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentattendancesubmit.php$ student/studentattendancesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkoff.php$ student/checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcheckoff.php$ student/addcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkoffdetail.php$ student/checkoffdetail.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkoffsubmit.php$ student/checkoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcheckoffsubmit.php$ student/addcheckoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/interaction.php$ student/interaction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/volunteerEvalList.php$ student/volunteerEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addVolunteerEval.php$ student/addVolunteerEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addVolunteerEvalSubmit.php$ student/addVolunteerEvalSubmit.php?schoolslug=$1 [QSA,L]


RewriteRule ^school\/([^/]*)\/student/addinteraction.php$ student/addinteraction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/interactionsubmit.php$ student/interactionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addjournal.php$ student/addjournal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/journalsubmit.php$ student/journalsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/journal.php$ student/journal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_rotation.php$ student/get_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/cievaluationlist.php$ student/cievaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/siteevaluationlist.php$ student/siteevaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/cievaluation.php$ student/cievaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/siteevaluation.php$ student/siteevaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/siteevaluationsubmit.php$ student/siteevaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/cievaluationsubmit.php$ student/cievaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/procedurecounts.php$ student/procedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addprocedurecountsubmit.php$ student/addprocedurecountsubmit.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/student/summativesubmit.php$ student/summativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/formativesubmit.php$ student/formativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/summative.php$ student/summative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/formative.php$ student/formative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/summativelist.php$ student/summativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/formativelist.php$ student/formativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcoarcsurvey.php$ student/addcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcoarcsurveysubmit.php$ student/addcoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_clinician.php$ student/get_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/graduatecoarcsurvey.php$ student/graduatecoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/graduatecoarcsurveysubmit.php$ student/graduatecoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/allcoarcsurvey.php$ student/allcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/showstudentnotification.php$ student/showstudentnotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentnotification.php$ student/studentnotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/procedurecounts.php$ student/procedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/Checkofflist.php$ student/Checkofflist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addprocedurecountsubmit.php$ student/addprocedurecountsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/incident.php$ student/incident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addincident.php$ student/addincident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addincidentsubmit.php$ student/addincidentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/Attendance.php$ student/Attendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/viewprocedurecounts.php$ student/viewprocedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/personnalcoarcsurveysubmit.php$ student/personnalcoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/personnalcoarcsurvey.php$ student/personnalcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/employercoarcsurvey.php$ student/employercoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/employercoarcsurveysubmit.php$ student/employercoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/getAllhospitalForDailyEval.php$ student/getAllhospitalForDailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_hospital.php$ student/get_hospital.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/midtermlist.php$ student/midtermlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/midterm.php$ student/midterm.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/midtermsubmit.php$ student/midtermsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkoffs.php$ student/checkoffs.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkofftopicstatus.php$ student/checkofftopicstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcheckoffs.php$ student/addcheckoffs.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcheckoffssubmit.php$ student/addcheckoffssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkofftopicstatus.php$ student/checkofftopicstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/dailyEvalList.php$ student/dailyEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/dailyEval.php$ student/dailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/dailyEvalSubmit.php$ student/dailyEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/ajax_get_student_Checkoff.php$ student/ajax_get_student_Checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_topics_for_procedure_count.php$ student/get_topics_for_procedure_count.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/checkoffusaf.php$ student/checkoffusaf.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addusafcheckoff.php$ student/addusafcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addusafcheckoffsubmit.php$ student/addusafcheckoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/caseStudyList.php$ student/caseStudyList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudy.php$ student/addCaseStudy.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudyAdult.php$ student/addCaseStudyAdult.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudyAdultSubmit.php$ student/addCaseStudyAdultSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudySubmit.php$ student/addCaseStudySubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/showTopicStepsInModel.php$ student/showTopicStepsInModel.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/masteryList.php$ student/masteryList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addMastery.php$ student/addMastery.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/masteryEvalSubmit.php$ student/masteryEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/viewBriefcase.php$ student/viewBriefcase.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/downloadBriefCaseFile.php$ student/downloadBriefCaseFile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/downloadBriefCaseFile.php$ student/downloadBriefCaseFile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcheckoffstandardsubmit.php$ student/addcheckoffstandardsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_student_rotation.php$ student/get_student_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_clinician_rotation.php$ student/get_clinician_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/get_topics_rotation.php$ student/get_topics_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/rotations1.php$ student/rotations1.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/clockintranssubmit1.php$ student/clockintranssubmit1.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addcoarcsurvey1.php$ student/addcoarcsurvey1.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/login_CoARC_Survay.php$ student/login_CoARC_Survay.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/coarcSubmitedSuccessfullyPage.php$ student/coarcSubmitedSuccessfullyPage.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/login_CoARC_Survey.php$ student/login_CoARC_Survey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/documentUpload.php$ student/documentUpload.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/documentUploadSubmit.php$ student/documentUploadSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/registerExternalPreceptor.php$ student/registerExternalPreceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/registerExternalPreceptorSubmit.php$ student/registerExternalPreceptorSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/thankyou.php$ student/thankyou.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/pefList.php$ student/pefList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addPEFEvaluation.php$ student/addPEFEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addPEFEvaluationsubmit.php$ student/addPEFEvaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/pevaluationlist.php$ student/pevaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addpevaluation.php$ student/addpevaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addpevaluationsubmit.php$ student/addpevaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/floorTherapyAndICUEvaluationlist.php$ student/floorTherapyAndICUEvaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addFloorTherapyAndICUEvaluation.php$ student/addFloorTherapyAndICUEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addFloorTherapyAndICUEvaluationSubmit.php$ student/addFloorTherapyAndICUEvaluationSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/assignFloorTherapyAndICUEvalToProceptor.php$ student/assignFloorTherapyAndICUEvalToProceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/assignEvaluationToProceptor.php$ student/assignEvaluationToProceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudyPACR.php$ student/addCaseStudyPACR.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentSubmitTrial.php$ student/studentSubmitTrial.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/studentMerge.php$ student/studentMerge.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCaseStudyPACRSubmit.php$ student/addCaseStudyPACRSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/activitySheet.php$ student/activitySheet.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/activitysheetsubmit.php$ student/activitysheetsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/activitysheetlist.php$ student/activitysheetlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/pdfViewForAllModules.php$ student/pdfViewForAllModules.php?schoolslug=$1 [QSA,L]

#Soap Note for Student
RewriteRule ^school\/([^/]*)\/student/soapNoteList.php$ student/soapNoteList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addSoapNote.php$ student/addSoapNote.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/soapNoteSubmit.php$ student/soapNoteSubmit.php?schoolslug=$1 [QSA,L]


#Self Activate Account 
RewriteRule ^school\/([^/]*)\/student/selfactivateaccount.php$ student/selfactivateaccount.php?schoolslug=$1 [QSA,L]
#Call Off
RewriteRule ^school\/([^/]*)\/student/callOffList.php$ student/callOffList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCallOff.php$ student/addCallOff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/student/addCallOffSubmit.php$ student/addCallOffSubmit.php?schoolslug=$1 [QSA,L]

#Website Rules
RewriteRule ^school\/([^/]*)\/index.php$ index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/$ index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)$ index.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/admin/dashboard.php$ admin/dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/countryCode.php$ admin/countryCode.php?schoolslug=$1 [QSA,L]


# Clinician Rules
RewriteRule ^school\/([^/]*)\/clinician/index.php$ clinician/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician$ clinician/index.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/$ clinician/index.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/clinician/dashboard.php$ clinician/dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/studentProcedureList.php$ clinician/studentProcedureList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/personnalcoarcsurvey.php$ clinician/personnalcoarcsurvey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/personnalcoarcsurveysubmit.php$ clinician/personnalcoarcsurveysubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/clinicianLoginsubmit.php$ clinician/clinicianLoginsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/logout.php$ clinician/logout.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/forgotpassword.php$ clinician/forgotpassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/forgotpasswordsubmit.php$ clinician/forgotpasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/changepassword.php$ clinician/changepassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/changepasswordsubmit.php$ clinician/changepasswordsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/editprofile.php$ clinician/editprofile.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/faqquestion.php$ clinician/faqquestion.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/editprofilesubmit.php$ clinician/editprofilesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/settings.php$ clinician/settings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/courses.php$ clinician/courses.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/students.php$ clinician/students.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/schoolstudents.php$ clinician/schoolstudents.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/rotations.php$ clinician/rotations.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addsubrotation.php$ clinician/addsubrotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/rotationstudents.php$ clinician/rotationstudents.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/Attendance.php$ clinician/Attendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/attendancesubmit.php$ clinician/attendancesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/calendar.php$ clinician/calendar.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/interaction.php$ clinician/interaction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addinteraction.php$ clinician/addinteraction.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/interactionsubmit.php$ clinician/interactionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/volunteerEvalList.php$ clinician/volunteerEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addVolunteerEval.php$ clinician/addVolunteerEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addVolunteerEvalSubmit.php$ clinician/addVolunteerEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/incident.php$ clinician/incident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addincident.php$ clinician/addincident.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addincidentsubmit.php$ clinician/addincidentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/clinicianattendance.php$ clinician/clinicianattendance.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/clinicianaddexception.php$ clinician/clinicianaddexception.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/clinicianaddexceptionsubmit.php$ clinician/clinicianaddexceptionsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/clinicianattendancelist.php$ admin/clinicianattendancelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/attendancelist.php$ admin/attendancelist.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/clinician/summativesubmit.php$ clinician/summativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/formativesubmit.php$ clinician/formativesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/summative.php$ clinician/summative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/formative.php$ clinician/formative.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/summativelist.php$ clinician/summativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/formativelist.php$ clinician/formativelist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/equipmentsubmit.php$ clinician/equipmentsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/equipment.php$ clinician/equipment.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/equipmentlist.php$ clinician/equipmentlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/irr.php$ clinician/irr.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addirr.php$ clinician/addirr.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/irrsubmit.php$ clinician/irrsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/rotationlist.php$ clinician/rotationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/showcliniciannotification.php$ clinician/showcliniciannotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/cliniciannotification.php$ clinician/cliniciannotification.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/map.php$ clinician/map.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkoffdetail.php$ clinician/checkoffdetail.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/studentCheckoffList.php$ clinician/studentCheckoffList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addcheckoff.php$ clinician/addcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkoff-grid-data.php$ clinician/checkoff-grid-data.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkoff.php$ clinician/checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkoffusaf.php$ clinician/checkoffusaf.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addusafcheckoff.php$ clinician/addusafcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addusafcheckoffsubmit.php$ clinician/addusafcheckoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addcheckoffsubmit.php$ clinician/addcheckoffsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/cievaluation.php$ clinician/cievaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/cievaluationlist.php$ clinician/cievaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/procedurecounts.php$ clinician/procedurecounts.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/journal.php$ clinician/journal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addjournal.php$ clinician/addjournal.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/journalsubmit.php$ clinician/journalsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/cievaluationsubmit.php$ clinician/cievaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/midtermlist.php$ clinician/midtermlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/midterm.php$ clinician/midterm.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/midtermsubmit.php$ clinician/midtermsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/irrreport.php$ clinician/irrreport.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/clinicianlist.php$ clinician/clinicianlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/irrreportview.php$ clinician/irrreportview.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkoffs.php$ clinician/checkoffs.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addcheckoffs.php$ clinician/addcheckoffs.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addcheckoffssubmit.php$ clinician/addcheckoffssubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/checkofftopicstatus.php$ clinician/checkofftopicstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/getAllhospitalForDailyEval.php$ clinician/getAllhospitalForDailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/dailyEval.php$ clinician/dailyEval.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/dailyEvalList.php$ clinician/dailyEvalList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/dailyEvalSubmit.php$ clinician/dailyEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/studentList.php$ clinician/studentList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/caseStudyList.php$ clinician/caseStudyList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudy.php$ clinician/addCaseStudy.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudyAdult.php$ clinician/addCaseStudyAdult.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudyAdultSubmit.php$ clinician/addCaseStudyAdultSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudySubmit.php$ clinician/addCaseStudySubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/showTopicStepsInModel.php$ clinician/showTopicStepsInModel.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/assignstudentsList.php$ clinician/assignstudentsList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/attendencePdf.php$ clinician/attendencePdf.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/masteryList.php$ clinician/masteryList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addMastery.php$ clinician/addMastery.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/masteryEvalSubmit.php$ clinician/masteryEvalSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/rotationstudentDetails.php$ clinician/rotationstudentDetails.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/login_CoARC_Survey.php$ clinician/login_CoARC_Survey.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/coarcSubmitedSuccessfullyPage.php$ clinician/coarcSubmitedSuccessfullyPage.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/pefList.php$ clinician/pefList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addPEFEvaluation.php$ clinician/addPEFEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addPEFEvaluationsubmit.php$ clinician/addPEFEvaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/pevaluationlist.php$ clinician/pevaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addpevaluation.php$ clinician/addpevaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/floorTherapyAndICUEvaluationlist.php$ clinician/floorTherapyAndICUEvaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addFloorTherapyAndICUEvaluation.php$ clinician/addFloorTherapyAndICUEvaluation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addFloorTherapyAndICUEvaluationSubmit.php$ clinician/addFloorTherapyAndICUEvaluationSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/registerExternalPreceptor.php$ clinician/registerExternalPreceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/registerExternalPreceptorSubmit.php$ clinician/registerExternalPreceptorSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/thankyou.php$ clinician/thankyou.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudyPACR.php$ clinician/addCaseStudyPACR.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCaseStudyPACRSubmit.php$ clinician/addCaseStudyPACRSubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/reports.php$ clinician/reports.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/reportsubmit.php$ clinician/reportsubmit.php?schoolslug=$1 [QSA,L]

#Soap Note for Clinician
RewriteRule ^school\/([^/]*)\/clinician/soapNoteList.php$ clinician/soapNoteList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addSoapNote.php$ clinician/addSoapNote.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/soapNoteSubmit.php$ clinician/soapNoteSubmit.php?schoolslug=$1 [QSA,L]

#activity sheet
RewriteRule ^school\/([^/]*)\/clinician/activitySheet.php$ clinician/activitySheet.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/activitysheetsubmit.php$ clinician/activitysheetsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/activitysheetlist.php$ clinician/activitysheetlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/studentListForClinician.php$ clinician/studentListForClinician.php?schoolslug=$1 [QSA,L]



#For Admin CI Evaluation
RewriteRule ^school\/([^/]*)\/clinician/admincievaluationlist.php$ clinician/admincievaluationlist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/admincievaluationsubmit.php$ clinician/admincievaluationsubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/admincievaluation.php$ clinician/admincievaluation.php?schoolslug=$1 [QSA,L]

#Call Off
RewriteRule ^school\/([^/]*)\/clinician/callOffList.php$ clinician/callOffList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/clinician/addCallOff.php$ clinician/addCallOff.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_list_to_clinician.php$ ajax/ajax_get_student_list_to_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_checkoff_to_report.php$ ajax/ajax_get_student_checkoff_to_report.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_list_to_admin.php$ ajax/ajax_get_student_list_to_admin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_checkoff_questions_to_clinician.php$ ajax/ajax_get_checkoff_questions_to_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/briefcasesubmit.php$ admin/briefcasesubmit.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_checkoff_questions_to_student.php$ ajax/ajax_get_checkoff_questions_to_student.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentAccreditationList.php$ admin/exportStudentAccreditationList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentAccreditationAllList.php$ admin/exportStudentAccreditationAllList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentCertificationLogList.php$ admin/exportStudentCertificationLogList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentAdditationlContactInformationAllList.php$ admin/exportStudentAdditationlContactInformationAllList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/viewBriefcase.php$ admin/viewBriefcase.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/addbriefcase.php$ admin/addbriefcase.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentAdditationlContactInformationList.php$ admin/exportStudentAdditationlContactInformationList.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/exportStudentCertificationLog.php$ admin/exportStudentCertificationLog.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_list_to_clinician.php$ ajax/ajax_get_student_list_to_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_checkoff_to_report.php$ ajax/ajax_get_student_checkoff_to_report.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_list_to_admin.php$ ajax/ajax_get_student_list_to_admin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_checkoff_questions_to_clinician.php$ ajax/ajax_get_checkoff_questions_to_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxcheckoff.php$ ajax/ajaxcheckoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxcliniciancheckofftopicstatus.php$ ajax/ajaxcliniciancheckofftopicstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_rotation_by_hospitalside.php$ ajax/ajax_get_rotation_by_hospitalside.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxprocedurecountstudents.php$ ajax/ajaxprocedurecountstudents.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxprocedurecount.php$ ajax/ajaxprocedurecount.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_delete_schoolHoliday.php$ ajax/ajax_delete_schoolHoliday.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_save_schoolHoliday.php$ ajax/ajax_save_schoolHoliday.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_update_editdate.php$ ajax/ajax_update_editdate.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxclinicianprocedurecount.php$ ajax/ajaxclinicianprocedurecount.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/send_employer_coarc_request.php$ ajax/send_employer_coarc_request.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_send_notes.php$ ajax/ajax_send_notes.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/getSubRotationnListInReports.php$ ajax/getSubRotationnListInReports.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_check_duplicate_email.php$ ajax/ajax_check_duplicate_email.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_update_hospital_status.php$ ajax/ajax_update_hospital_status.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_studetRecordId.php$ ajax/ajax_studetRecordId.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/send_standard_checkoff_sms_to_preceptor.php$ ajax/send_standard_checkoff_sms_to_preceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_send_record_to_canvas.php$ ajax/ajax_send_record_to_canvas.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_updateStudentProgramDates.php$ ajax/ajax_updateStudentProgramDates.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_Preceptor_checkoff_to_report.php$ ajax/ajax_get_student_Preceptor_checkoff_to_report.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_absence_in_dashboard.php$ ajax/ajax_get_student_absence_in_dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_Update_checkoffstatus.php$ ajax/ajax_Update_checkoffstatus.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_updateCasestudySettings.php$ ajax/ajax_updateCasestudySettings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_add_caseStudy_reviews.php$ ajax/ajax_add_caseStudy_reviews.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_checkoff_steps.php$ ajax/ajax_get_checkoff_steps.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/send_evaluation_sms_to_preceptor.php$ ajax/send_evaluation_sms_to_preceptor.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_reports.php$ ajax/ajax_get_reports.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_updateEmailToPassword.php$ ajax/ajax_updateEmailToPassword.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_validate_student.php$ ajax/ajax_validate_student.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_copy_link.php$ ajax/ajax_copy_link.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_SaveSchedule.php$ ajax/ajax_SaveSchedule.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_user_details.php$ ajax/ajax_get_user_details.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_change_schedule_hospital_site.php$ ajax/ajax_change_schedule_hospital_site.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_Advance_preceptor_details.php$ ajax/ajax_get_Advance_preceptor_details.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_assign_preceptor_to_checkoff.php$ ajax/ajax_assign_preceptor_to_checkoff.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_update_course_status.php$ ajax/ajax_update_course_status.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_clinician_list_to_admin.php$ ajax/ajax_get_clinician_list_to_admin.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_send_approudhours_of_clinician.php$ ajax/ajax_send_approudhours_of_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_send_notes_clinician.php$ ajax/ajax_send_notes_clinician.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_users_details_for_dashboard.php$ ajax/ajax_get_users_details_for_dashboard.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_check_coarcsurveytitle_exist.php$ ajax/ajax_check_coarcsurveytitle_exist.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_repeatondays_list.php$ ajax/ajax_get_repeatondays_list.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_userrolewise.php$ ajax/ajax_get_userrolewise.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_update_report_status.php$ ajax/ajax_update_report_status.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_self_activate_account.php$ ajax/ajax_self_activate_account.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_showTopicStepsInModel.php$ ajax/ajax_showTopicStepsInModel.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_country_code.php$ ajax/ajax_get_country_code.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_program_dates.php$ ajax/ajax_get_program_dates.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_updateSchoolSettings.php$ ajax/ajax_updateSchoolSettings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_authenticat_chat_user.php$ ajax/ajax_authenticat_chat_user.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_chat_notifications.php$ ajax/ajax_get_chat_notifications.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_send_device_token.php$ ajax/ajax_send_device_token.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_user_chat_access_token.php$ ajax/ajax_get_user_chat_access_token.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/ajax/ajax_authenticat_audit_log.php$ ajax/ajax_authenticat_audit_log.php?schoolslug=$1 [QSA,L]



#Activity Sheet 
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_hospitalsites_by_rotation.php$ ajax/ajax_get_hospitalsites_by_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_clinical_instructor_by_rotation.php$ ajax/ajax_get_clinical_instructor_by_rotation.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajaxprocedurecountstudentsforactivitysheet.php$ ajax/ajaxprocedurecountstudentsforactivitysheet.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_get_student_Procedure_Count_Report.php$ ajax/ajax_get_student_Procedure_Count_Report.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_student_procedure_count.php$ ajax/ajax_student_procedure_count.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/admin/procedurecountsNew1.php$ admin/procedurecountsNew1.php?schoolslug=$1 [QSA,L]

RewriteRule ^school\/([^/]*)\/ajax/ajax_show_activitysheet_procedurecounts.php$ ajax/ajax_show_activitysheet_procedurecounts.php?schoolslug=$1 [QSA,L]
#RewriteRule ^school\/([^/]*)\/ajax/ajax_get_user_details_by_email.php$ ajax/ajax_get_user_details_by_email.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_save_call_off_notification_settings.php$ ajax/ajax_save_call_off_notification_settings.php?schoolslug=$1 [QSA,L]
RewriteRule ^school\/([^/]*)\/ajax/ajax_save_case_study.php$ ajax/ajax_save_case_study.php?schoolslug=$1 [QSA,L]

# BEGIN Expires
<ifModule mod_expires.c>
ExpiresActive On
ExpiresDefault "access plus 1 seconds"
ExpiresByType text/html "access plus 1 seconds"
ExpiresByType image/gif "access plus 2592000 seconds"
ExpiresByType image/jpeg "access plus 2592000 seconds"
ExpiresByType image/png "access plus 2592000 seconds"
ExpiresByType text/css "access plus 604800 seconds"
ExpiresByType text/javascript "access plus 216000 seconds"
ExpiresByType application/x-javascript "access plus 216000 seconds"
</ifModule>
# END Expires

# BEGIN cPanel-generated php ini directives, do not edit
# Manual editing of this file may result in unexpected behavior.
# To make changes to this file, use the cPanel MultiPHP INI Editor (Home >> Software >> MultiPHP INI Editor)
# For more information, read our documentation (https://go.cpanel.net/EA4ModifyINI)
<IfModule php7_module>
   php_flag display_errors on
   php_value max_execution_time 900
   php_value max_input_time 120
   php_value max_input_vars 2000
   php_value memory_limit 768M
   php_value post_max_size 512M
   php_value session.gc_maxlifetime 1440
   php_value upload_max_filesize 512M
   php_flag zlib.output_compression Off
</IfModule>
<IfModule lsapi_module>
   php_flag display_errors Off
   php_value max_execution_time 900
   php_value max_input_time 120
   php_value max_input_vars 2000
   php_value memory_limit 768M
   php_value post_max_size 512M
   php_value session.gc_maxlifetime 1440
   php_value upload_max_filesize 512M
   php_flag zlib.output_compression Off
</IfModule>
# END cPanel-generated php ini directives, do not edit

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php71” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php71 .php .php7 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit

<FilesMatch "^\.env">
  Order allow,deny
  Deny from all
</FilesMatch>
 
 <Files "secret-manager-key.json">
    Order Allow,Deny
    Deny from all
</Files>

#denies access to any URL that starts with /etc/
RewriteRule ^etc/ - [F,L]

<Files "secret_loader.php">
  Order allow,deny
  Deny from all
</Files>
