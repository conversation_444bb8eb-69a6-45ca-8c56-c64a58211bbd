<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsJournal.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsActivitysheet.php');
include('../class/clsExternalPreceptors.php');


$selrotationId = 0;
$rotationId = 0;

$TimeZone =  $_SESSION["loggedStudentSchoolTimeZone"];
$studentId = $_SESSION["loggedStudentId"];
//Get All Activity Sheet List
$objActivitySheet = new clsActivitysheet();
$rowsActivitySheetData = $objActivitySheet->GetAllActivitySheet($currentSchoolId, $studentId);
$totalActivityCount = 0;
if ($rowsActivitySheetData != '') {
    $totalActivityCount = mysqli_num_rows($rowsActivitySheetData);
}
unset($objActivitySheet);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($rotationId);


$objDB = new clsDB();
$loggedStudentEmailId = $objDB->GetSingleColumnValueFromTable('student', 'email', 'studentId', $studentId);
unset($objDB);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Activity Sheet</title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">


    <style>
        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #01A750; */
            /* color: #fff; */
            color: #555;
            background: #f6f9f9;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Activity Sheet</li>
                </ol>
            </div>
            <?php if ($rotationStatus == 0) { ?>
                <div class="pull-right" style="margin-top:5px">
                    <a href="activitySheet.html">Add</a>
                </div>
            <?php } ?>

        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            $message = "";
            $alertType = "alert-success";

            switch ($_GET["status"]) {
                case "Added":
                    $message = "Activity Sheet added successfully.";
                    break;
                case "Updated":
                    $message = "Activity Sheet updated successfully.";
                    break;
                case "Error":
                    $message = "Error occurred.";
                    $alertType = "alert-danger";
                    break;
            }

            if ($message) {
                echo '<div class="alert ' . $alertType . ' alert-dismissible fade in" role="alert">';
                echo '<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>';
                echo $message;
                echo '</div>';
            }
        }
        ?>


        <div id="divTopLoading">Loading...</div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Clinician/Preceptor</th>
                    <th style="text-align: center">Amount of<br>Time Spent </th>
                    <th style="text-align: center">Points <br> Awarded</th>
                    <th style="text-align: center">Procedure <br> Count</th>
                    <th style="text-align: center">Clinician/Preceptor <br>Response</th>
                    <th>Clinician/Preceptor <br>Sign Date </th>
                    <th>School Sign Date </th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalActivityCount > 0) {
                    while ($row = mysqli_fetch_array($rowsActivitySheetData)) {

                        $activityId = ($row['activityId']);
                        $activityDate = stripslashes($row['activityDate']);
                        $rotationName = stripslashes($row['rotationName']);
                        $isSendToPreceptor = stripslashes($row['isSendToPreceptor']);
                        $externalPreceptorId = stripslashes($row['preceptorId']);
                        $clinicianName = stripslashes($row['clinicianName']);

                        $rotationId = $row['rotationId'];
                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $interactionId = stripslashes($row['interactionId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objrotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        // Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

                        // $activityDate = converFromServerTimeZone($activityDate, $TimeZone);
                        $activityDate = date("m/d/Y", strtotime($activityDate));
                        $clinicianComment = stripslashes($row['clinicianComment']);
                        $clinicianSignatureDate = stripslashes($row['clinicianSignatureDate']);

                        if ($clinicianSignatureDate != '12/31/1969' && $clinicianSignatureDate != '' && $clinicianSignatureDate != '11/30/-0001' && $clinicianSignatureDate != '0000-00-00') {

                            $clinicianSignatureDate = date("m/d/Y", strtotime($clinicianSignatureDate));
                        } else
                            $clinicianSignatureDate = '-';


                        $schoolSignatureDate = stripslashes($row['schoolSignatureDate']);
                        if ($schoolSignatureDate != '12/31/1969' && $schoolSignatureDate != '' && $schoolSignatureDate != '11/30/-0001' && $schoolSignatureDate != '0000-00-00') {

                            $schoolSignatureDate = date("m/d/Y", strtotime($schoolSignatureDate));
                        } else
                            $schoolSignatureDate = '-';

                        if ($clinicianComment != '') {
                            $Response = 'Yes';
                        } else {
                            $Response = 'No';
                        }
                        $isCompletedStatus = '';
                        $objExternalPreceptors = new clsExternalPreceptors();

                        $isPreceptorCompletedStatus  = stripslashes($row['isPreceptorCompletedStatus']);

                        $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($externalPreceptorId);
                        $preceptorId = isset($externalPreceptorDetail['id']) ? $externalPreceptorDetail['id'] : 0;
                        $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                        $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                        $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                        $formatted_number = '';
                        if ($preceptorMobileNum != '') {
                            $mobileNum = str_replace("-", "", $preceptorMobileNum);

                            $masked_number = substr_replace($mobileNum, "XXXXXX", 0, 6);
                            $formatted_number = substr($masked_number, 0, 3) . "-" . substr($masked_number, 3, 3) . "-" . substr($mobileNum, 6);
                        }
                        $isCompletedStatus = $isPreceptorCompletedStatus ? 'Completed' : 'Pending';
                        // $preceptorFullName = $isPreceptorCompletedStatus ? $preceptorFullName : '';
                        $objDB = new clsDB();
                        $interactionDetails = $objDB->GetSelectedColumnsFromTable('interaction', ["timeSpent", "pointsAwarded"], 'interactionId', $interactionId);
                        unset($objDB);
                        $pointsAwarded = isset($interactionDetails['pointsAwarded']) ? $interactionDetails['pointsAwarded'] : '';
                        $timeSpent = isset($interactionDetails['timeSpent']) ? $interactionDetails['timeSpent'] : '';

                        $procedurecountIds = isset($row['procedurecountId']) ? $row['procedurecountId'] : '';

                ?>
                        <tr>

                            <td>
                                <?php echo ($activityDate);
                                ?>
                            </td>

                            <td>
                                <?php echo ($rotationName);
                                ?>
                            </td>
                            <td class="<?php if ($preceptorMobileNum == 0) {
                                            echo 'text-center';
                                        }  ?>">
                                <?php if ($isSendToPreceptor) { ?>
                                    Name: <?php echo $preceptorFullName; ?> <br>
                                    Phone: <?php echo $formatted_number; ?> <br>
                                    Status: <?php echo $isCompletedStatus; ?> <br>
                                    <?php if ($isPreceptorCompletedStatus != 1) {
                                    ?>
                                        <a href="javascript:void(0);" activityId="<?php echo $activityId; ?>" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> rotationId="<?php echo $rotationId ?>" class="reSendrequest" data-toggle="modal" data-target="#resendModal" title="Resend Activtiy Sheet" onclick="ShowEvaluationDetails(this)">Resend SMS</a>
                                        | <a href="javascript:void(0);" activityId="<?php echo $activityId; ?>" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> class="copyLink" evaluationType='activitySheet' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                        <br><a href="javascript:void(0);" activityId="<?php echo $activityId; ?>" evaluationType="activitySheet" preceptorMobileNum="<?php echo $preceptorMobileNum; ?>" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> class="reSendEmailrequest" data-toggle="modal" email="<?php echo $loggedStudentEmailId; ?>" data-target="#resendEmailModal" onclick="ShowEvaluationDetailsForEmail(this)">Send URL to Email</a>
                                    <?php }
                                } else { ?>
                                    <?php echo $clinicianName; ?>
                                <?php } ?>
                            </td>
                            <td align="center">
                                <?php echo $timeSpent; ?>
                            </td>
                            <td align="center">
                                <?php echo $pointsAwarded; ?>
                            </td>
                            <td align="center">
                                <?php if ($procedurecountIds != '') { ?>
                                    <a href="javascript:void(0);" class="getProcedureCount" activityId="<?php echo EncodeQueryData($activityId) ?>">Yes</a>
                                <?php } else { ?>
                                    No
                                <?php } ?>

                            </td>
                            <td align="center">
                                <?php echo ($Response); ?>
                            </td>
                            <td align="center">
                                <?php echo ($clinicianSignatureDate); ?>
                            </td>
                            <td align="center">
                                <?php echo ($schoolSignatureDate); ?>
                            </td>
                            <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                                if ($clinicianSignatureDate != '-' ||  $rotationStatus) { ?>
                                    <a href="activitySheet.html?editactivityid=<?php echo (EncodeQueryData($activityId)); ?>&view=V">View</a>
                                <?php } else { ?>
                                    <a href="activitySheet.html?editactivityid=<?php echo (EncodeQueryData($activityId)); ?>">Edit</a>
                                <?php } ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objrotation);
                ?>
            </tbody>

        </table>
    </div>

    <?php include('includes/resendSms.php'); ?>
    <?php include('includes/resendLinkToEmail.php'); ?>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/activitysheetProcedureCount.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script> -->

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        // $('#journallist').parsley().on('field:validated', function() {
        //     var ok = $('.parsley-error').length === 0;
        // });
        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();


            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $("#cborotation").change(function() {
                var rotationId = $(this).val();
                if (rotationId) {
                    window.location.href = "journal.html?rotationId=" + rotationId;
                } else {
                    window.location.href = "journal.html";
                }
            });


        });
        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            "responsive": false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "20%"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "width": "10%",
                    "className": "alignCenter",
                    "orderable": false
                },
                {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        function ShowEvaluationDetails(eleObj) {

            var preceptorId = $(eleObj).attr('preceptorId');
            var preceptorNum = $(eleObj).attr('preceptorMobileNum');
            var activityId = $(eleObj).attr('activityId');
            var rotationId = $(eleObj).attr('rotationId');
            var title = $(eleObj).attr('title');

            $('#preceptorId').val(preceptorId);
            $('#preceptorNum').val(preceptorNum);
            $('#activityId').val(activityId);
            $('#rotationId').val(rotationId);
            $('#evaluationType').val('activitySheet');
            $('#title').text(title);


        }

        $(document).on('click', '.btnSendSms', function() {
            var data = $('#resendForm').serialize();

            var mobileNo = $('#preceptorNum').val();
            var rsmobileNo = mobileNo.replace(/[_-]/g, '');
            var mobileNoLength = rsmobileNo.length;

            if (mobileNoLength != 10) {
                alertify.error('Invalid Mob Number')
                return false;
            }

            $.ajax({
                type: "POST",
                url: "../ajax/send_evaluation_sms_to_preceptor.html",
                data: {
                    data
                },
                success: function(data) {
                    alertify.success('Sent');
                    // console.log(data);
                    // window.location.reload();
                    setTimeout(function() {
                        // window.location.reload();
                    }, 2000);
                    // history.go(0);


                }
            });


        });
    </script>
</body>

</html>