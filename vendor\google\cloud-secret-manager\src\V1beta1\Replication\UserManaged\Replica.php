<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secrets/v1beta1/resources.proto

namespace Google\Cloud\SecretManager\V1beta1\Replication\UserManaged;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents a Replica for this [Secret][google.cloud.secrets.v1beta1.Secret].
 *
 * Generated from protobuf message <code>google.cloud.secrets.v1beta1.Replication.UserManaged.Replica</code>
 */
class Replica extends \Google\Protobuf\Internal\Message
{
    /**
     * The canonical IDs of the location to replicate data.
     * For example: `"us-east1"`.
     *
     * Generated from protobuf field <code>string location = 1;</code>
     */
    private $location = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $location
     *           The canonical IDs of the location to replicate data.
     *           For example: `"us-east1"`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Secrets\V1Beta1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * The canonical IDs of the location to replicate data.
     * For example: `"us-east1"`.
     *
     * Generated from protobuf field <code>string location = 1;</code>
     * @return string
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * The canonical IDs of the location to replicate data.
     * For example: `"us-east1"`.
     *
     * Generated from protobuf field <code>string location = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkString($var, True);
        $this->location = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Replica::class, \Google\Cloud\SecretManager\V1beta1\Replication_UserManaged_Replica::class);

