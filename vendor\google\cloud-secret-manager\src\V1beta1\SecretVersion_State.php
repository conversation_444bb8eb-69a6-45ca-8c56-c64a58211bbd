<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secrets/v1beta1/resources.proto

namespace Google\Cloud\SecretManager\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\SecretManager\V1beta1\SecretVersion\State instead.
     * @deprecated
     */
    class SecretVersion_State {}
}
class_exists(SecretVersion\State::class);
@trigger_error('Google\Cloud\SecretManager\V1beta1\SecretVersion_State is deprecated and will be removed in a future release. Use Google\Cloud\SecretManager\V1beta1\SecretVersion\State instead', E_USER_DEPRECATED);

