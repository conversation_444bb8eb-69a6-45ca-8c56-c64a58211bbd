<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\SecretManager\V1\ReplicationStatus\UserManagedStatus\ReplicaStatus instead.
     * @deprecated
     */
    class ReplicationStatus_UserManagedStatus_ReplicaStatus {}
}
class_exists(ReplicationStatus\UserManagedStatus\ReplicaStatus::class);
@trigger_error('Google\Cloud\SecretManager\V1\ReplicationStatus_UserManagedStatus_ReplicaStatus is deprecated and will be removed in a future release. Use Google\Cloud\SecretManager\V1\ReplicationStatus\UserManagedStatus\ReplicaStatus instead', E_USER_DEPRECATED);

