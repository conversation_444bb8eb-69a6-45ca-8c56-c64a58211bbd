<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\SecretManager\V1\ReplicationStatus\AutomaticStatus instead.
     * @deprecated
     */
    class ReplicationStatus_AutomaticStatus {}
}
class_exists(ReplicationStatus\AutomaticStatus::class);
@trigger_error('Google\Cloud\SecretManager\V1\ReplicationStatus_AutomaticStatus is deprecated and will be removed in a future release. Use Google\Cloud\SecretManager\V1\ReplicationStatus\AutomaticStatus instead', E_USER_DEPRECATED);

