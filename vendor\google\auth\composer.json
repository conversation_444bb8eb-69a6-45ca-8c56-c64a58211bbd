{"name": "google/auth", "type": "library", "description": "Google Auth Library for PHP", "keywords": ["google", "oauth2", "authentication"], "homepage": "http://github.com/google/google-auth-library-php", "license": "Apache-2.0", "support": {"docs": "https://googleapis.github.io/google-auth-library-php/main/"}, "require": {"php": "^7.4||^8.0", "firebase/php-jwt": "^6.0", "guzzlehttp/guzzle": "^6.5.8||^7.4.5", "guzzlehttp/psr7": "^2.4.5", "psr/http-message": "^1.1||^2.0", "psr/cache": "^1.0||^2.0||^3.0"}, "require-dev": {"guzzlehttp/promises": "^2.0", "squizlabs/php_codesniffer": "^3.5", "phpunit/phpunit": "^9.0.0", "phpspec/prophecy-phpunit": "^2.0", "sebastian/comparator": ">=1.2.3", "phpseclib/phpseclib": "^3.0", "kelvinmo/simplejwt": "0.7.1"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "autoload-dev": {"psr-4": {"Google\\Auth\\Tests\\": "tests"}}}