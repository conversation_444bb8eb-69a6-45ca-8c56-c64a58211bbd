{"name": "google/gax", "type": "library", "description": "Google API Core for PHP", "keywords": ["google"], "homepage": "https://github.com/googleapis/gax-php", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": ">=7.4", "google/auth": "^1.34.0", "google/grpc-gcp": "^0.2||^0.3", "grpc/grpc": "^1.13", "google/protobuf": "^3.22", "guzzlehttp/promises": "^1.4||^2.0", "guzzlehttp/psr7": "^2.0", "google/common-protos": "^4.4", "google/longrunning": "~0.2"}, "require-dev": {"phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "3.*", "phpspec/prophecy-phpunit": "^2.0"}, "conflict": {"ext-protobuf": "<3.7.0"}, "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\ApiCore\\": "metadata/ApiCore"}}, "autoload-dev": {"psr-4": {"Google\\ApiCore\\Dev\\": "dev/src", "Google\\ApiCore\\": "tests", "GPBMetadata\\Google\\": "metadata/Google"}}, "scripts": {"regenerate-test-protos": "dev/sh/regenerate-test-protos.sh"}}