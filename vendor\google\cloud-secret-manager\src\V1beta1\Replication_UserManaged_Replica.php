<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secrets/v1beta1/resources.proto

namespace Google\Cloud\SecretManager\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\SecretManager\V1beta1\Replication\UserManaged\Replica instead.
     * @deprecated
     */
    class Replication_UserManaged_Replica {}
}
class_exists(Replication\UserManaged\Replica::class);
@trigger_error('Google\Cloud\SecretManager\V1beta1\Replication_UserManaged_Replica is deprecated and will be removed in a future release. Use Google\Cloud\SecretManager\V1beta1\Replication\UserManaged\Replica instead', E_USER_DEPRECATED);

