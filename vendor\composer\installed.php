<?php return array(
    'root' => array(
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '33d1a7ee590c58204ab12e20be0a17bf92afe892',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '33d1a7ee590c58204ab12e20be0a17bf92afe892',
            'dev_requirement' => false,
        ),
        'clue/stream-filter' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'reference' => '049509fef80032cb3f051595029ab75b49a3c2f7',
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.0',
            'version' => '6.10.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => 'a49db6f0a5033aef5143295342f1c95521b075ff',
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.37.2',
            'version' => '1.37.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'reference' => '25eed0045d8cf107424a8b9010c9fdcc0734ceb0',
            'dev_requirement' => false,
        ),
        'google/cloud-secret-manager' => array(
            'pretty_version' => 'v1.13.1',
            'version' => '1.13.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/cloud-secret-manager',
            'aliases' => array(),
            'reference' => 'd6aeb26ff04a0dd6177704c5741467b81aad7b3d',
            'dev_requirement' => false,
        ),
        'google/common-protos' => array(
            'pretty_version' => 'v4.5.0',
            'version' => '4.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/common-protos',
            'aliases' => array(),
            'reference' => 'dfc232e90823cedca107b56e7371f2e2f35b9427',
            'dev_requirement' => false,
        ),
        'google/gax' => array(
            'pretty_version' => 'v1.29.2',
            'version' => '1.29.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/gax',
            'aliases' => array(),
            'reference' => 'e400e2432475f0ab85d3fdd6eddbc995ba787a21',
            'dev_requirement' => false,
        ),
        'google/grpc-gcp' => array(
            'pretty_version' => 'v0.3.0',
            'version' => '0.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/grpc-gcp',
            'aliases' => array(),
            'reference' => '4d8b455a45a89f57e1552cadc41a43bc34c40456',
            'dev_requirement' => false,
        ),
        'google/longrunning' => array(
            'pretty_version' => '0.4.7',
            'version' => '0.4.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/longrunning',
            'aliases' => array(),
            'reference' => '624cabb874c10e5ddc9034c999f724894b70a3d3',
            'dev_requirement' => false,
        ),
        'google/protobuf' => array(
            'pretty_version' => 'v3.25.8',
            'version' => '3.25.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/protobuf',
            'aliases' => array(),
            'reference' => '57d440fc54a00fda5b8781e8d9bf0140ea6d8e52',
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'reference' => '0690bde05318336c7221785f2a932467f98b64ca',
            'dev_requirement' => false,
        ),
        'grpc/grpc' => array(
            'pretty_version' => '1.57.0',
            'version' => '1.57.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grpc/grpc',
            'aliases' => array(),
            'reference' => 'b610c42022ed3a22f831439cb93802f2a4502fdf',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'dev_requirement' => false,
        ),
        'mailgun/mailgun-php' => array(
            'pretty_version' => 'v4.3.5',
            'version' => '4.3.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mailgun/mailgun-php',
            'aliases' => array(),
            'reference' => '3b1e971a572e55efa3d97b6a3f48c9e3683dcae6',
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'reference' => 'c3131244e29c08d44fefb49e0dd35021e9e39dd2',
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'reference' => '44bb1ab01811116f01fe216ab37d921dccc6c10d',
            'dev_requirement' => false,
        ),
        'nyholm/psr7' => array(
            'pretty_version' => '1.8.2',
            'version' => '1.8.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nyholm/psr7',
            'aliases' => array(),
            'reference' => 'a71f2b11690f4b24d099d6b16690a90ae14fc6f3',
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'php-http/client-common' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/client-common',
            'aliases' => array(),
            'reference' => '0cfe9858ab9d3b213041b947c881d5b19ceeca46',
            'dev_requirement' => false,
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'php-http/curl-client' => array(
            'pretty_version' => '2.3.3',
            'version' => '2.3.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/curl-client',
            'aliases' => array(),
            'reference' => 'f3eb48d266341afec0229a7a37a03521d3646b81',
            'dev_requirement' => false,
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.20.0',
            'version' => '1.20.0.0',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'reference' => '82fe4c73ef3363caed49ff8dd1539ba06044910d',
            'dev_requirement' => false,
        ),
        'php-http/guzzle7-adapter' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/guzzle7-adapter',
            'aliases' => array(),
            'reference' => '03a415fde709c2f25539790fecf4d9a31bc3d0eb',
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'reference' => '5cad731844891a4c282f3f3e1b582c46839d22f4',
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.16.2',
            'version' => '********',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'reference' => '06dd5e8562f84e641bf929bfe699ee0f5ce8080a',
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/multipart-stream-builder' => array(
            'pretty_version' => '1.4.2',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/multipart-stream-builder',
            'aliases' => array(),
            'reference' => '10086e6de6f53489cca5ecc45b6f468604d3460e',
            'dev_requirement' => false,
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '*******',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '1.12.0',
            'version' => '********',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'reference' => 'f79611d6dc1f6b7e8e30b738fc371b392001dbfd',
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.8.1',
            'version' => '1.8.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'reference' => 'eab7a0df01fe2344d172bff4cd6dbd3f8b84ad15',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'sendgrid/php-http-client' => array(
            'pretty_version' => '3.14.0',
            'version' => '3.14.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/php-http-client',
            'aliases' => array(),
            'reference' => '7880d5aecc53856802130ba83af1dfcf942e9767',
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid' => array(
            'pretty_version' => '7.11.1',
            'version' => '7.11.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/sendgrid',
            'aliases' => array(),
            'reference' => '071eac6de115e8c0ead18a7ebdad7c19043f07e4',
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid-php' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'starkbank/ecdsa' => array(
            'pretty_version' => '0.0.5',
            'version' => '0.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../starkbank/ecdsa',
            'aliases' => array(),
            'reference' => '484bedac47bac4012dc73df91da221f0a66845cb',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v2.5.4',
            'version' => '2.5.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '605389f2a7e5625f273b53960dc46aeaf9c62918',
            'dev_requirement' => false,
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v5.4.45',
            'version' => '5.4.45.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'reference' => '74e5b6f0db3e8589e6cfd5efb317a1fc2bb52fb6',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => '0424dff1c58f028c451efff2045f5d92410bd540',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => 'fd22ab50000ef01661e2a31d850ebaa297f8e03c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'reference' => '0f68c03565dcaaf25a890667542e8bd75fe7e5bb',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '77fa7995ac1b21ab60769b7323d600a991a90433',
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'reference' => '1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7',
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'dev_requirement' => false,
        ),
    ),
);
