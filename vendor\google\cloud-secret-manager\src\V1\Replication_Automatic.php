<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\SecretManager\V1\Replication\Automatic instead.
     * @deprecated
     */
    class Replication_Automatic {}
}
class_exists(Replication\Automatic::class);
@trigger_error('Google\Cloud\SecretManager\V1\Replication_Automatic is deprecated and will be removed in a future release. Use Google\Cloud\SecretManager\V1\Replication\Automatic instead', E_USER_DEPRECATED);

