<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/client.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Required information for every language.
 *
 * Generated from protobuf message <code>google.api.CommonLanguageSettings</code>
 */
class CommonLanguageSettings extends \Google\Protobuf\Internal\Message
{
    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @deprecated
     */
    protected $reference_docs_uri = '';
    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     */
    private $destinations;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $reference_docs_uri
     *           Link to automatically generated reference documentation.  Example:
     *           https://cloud.google.com/nodejs/docs/reference/asset/latest
     *     @type array<int>|\Google\Protobuf\Internal\RepeatedField $destinations
     *           The destination where API teams want this client library to be published.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Client::initOnce();
        parent::__construct($data);
    }

    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @return string
     * @deprecated
     */
    public function getReferenceDocsUri()
    {
        @trigger_error('reference_docs_uri is deprecated.', E_USER_DEPRECATED);
        return $this->reference_docs_uri;
    }

    /**
     * Link to automatically generated reference documentation.  Example:
     * https://cloud.google.com/nodejs/docs/reference/asset/latest
     *
     * Generated from protobuf field <code>string reference_docs_uri = 1 [deprecated = true];</code>
     * @param string $var
     * @return $this
     * @deprecated
     */
    public function setReferenceDocsUri($var)
    {
        @trigger_error('reference_docs_uri is deprecated.', E_USER_DEPRECATED);
        GPBUtil::checkString($var, True);
        $this->reference_docs_uri = $var;

        return $this;
    }

    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getDestinations()
    {
        return $this->destinations;
    }

    /**
     * The destination where API teams want this client library to be published.
     *
     * Generated from protobuf field <code>repeated .google.api.ClientLibraryDestination destinations = 2;</code>
     * @param array<int>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setDestinations($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::ENUM, \Google\Api\ClientLibraryDestination::class);
        $this->destinations = $arr;

        return $this;
    }

}

