{"name": "google/cloud-secret-manager", "description": "Secret Manager Client for PHP", "license": "Apache-2.0", "minimum-stability": "stable", "autoload": {"psr-4": {"Google\\Cloud\\SecretManager\\": "src", "GPBMetadata\\Google\\Cloud\\Secrets\\": "metadata", "GPBMetadata\\Google\\Cloud\\Secretmanager\\": "metadata"}}, "autoload-dev": {"psr-4": {"Google\\Cloud\\SecretManager\\Tests\\": "tests"}}, "extra": {"component": {"id": "cloud-secret-manager", "path": "SecretManager", "entry": null, "target": "googleapis/google-cloud-php-secret-manager.git"}}, "require": {"php": ">=7.4", "google/gax": "^1.26.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "suggest": {"ext-grpc": "Enables use of gRPC, a universal high-performance RPC framework created by Google.", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}}