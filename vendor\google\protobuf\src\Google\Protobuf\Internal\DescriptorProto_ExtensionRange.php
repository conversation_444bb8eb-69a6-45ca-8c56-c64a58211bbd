<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/descriptor.proto

namespace Google\Protobuf\Internal;

if (false) {
    /**
     * This class is deprecated. Use Google\Protobuf\Internal\DescriptorProto\ExtensionRange instead.
     * @deprecated
     */
    class DescriptorProto_ExtensionRange {}
}
class_exists(DescriptorProto\ExtensionRange::class);
@trigger_error('Google\Protobuf\Internal\DescriptorProto_ExtensionRange is deprecated and will be removed in the next major release. Use Google\Protobuf\Internal\DescriptorProto\ExtensionRange instead', E_USER_DEPRECATED);

