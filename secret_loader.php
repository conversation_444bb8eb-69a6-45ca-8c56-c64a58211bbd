<?php

require 'vendor/autoload.php';

use Google\Cloud\SecretManager\V1\SecretManagerServiceClient;

/**
 * Load secrets from Google Secret Manager using a service account key file.
 *
 * @param array $secretKeys List of secret names.
 * @param string $projectId Google Cloud project ID.
 * @param string $credentialsPath Path to the JSON key file.
 * @return array Associative array of secrets.
 */
function loadSecrets(array $secretKeys, string $projectId, string $credentialsPath): array
{
    // Load credentials from JSON file
    $credentials = json_decode(file_get_contents($credentialsPath), true);

    // Initialize the Secret Manager client with explicit credentials
    $client = new SecretManagerServiceClient([
        'credentials' => $credentials,
    ]);

    $secrets = [];

    foreach ($secretKeys as $key) {
        try {
            $name = $client->secretVersionName($projectId, $key, 'latest');
            $response = $client->accessSecretVersion($name);
            $secrets[$key] = $response->getPayload()->getData();
        } catch (Exception $e) {
            error_log("Error fetching secret '$key': " . $e->getMessage());
            $secrets[$key] = null;
        }
    }

    return $secrets;
}

// Define your project ID and path to credentials JSON
$projectId = 'clinical-trac-rt'; // Replace with your actual GCP project ID
$credentialsPath = __DIR__ . '/etc/gcloud/secret.json'; // Adjust if path is different

// Define the secrets you want to fetch
$secretKeys = [
    'RT_UAT_DB_HOST',
    'RT_UAT_DB_USER',
    'RT_UAT_DB_PASSWORD',
    'DB_NAME',
    'API_KEY',
];

// Load secrets
$config = loadSecrets($secretKeys, $projectId, $credentialsPath);

// Example: Use secrets (like connecting to DB)
print "<pre>";
print_r($config);
print "</pre>";
