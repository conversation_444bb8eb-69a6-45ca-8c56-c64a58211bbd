<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Describes the status of customer-managed encryption.
 *
 * Generated from protobuf message <code>google.cloud.secretmanager.v1.CustomerManagedEncryptionStatus</code>
 */
class CustomerManagedEncryptionStatus extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The resource name of the Cloud KMS CryptoKeyVersion used to
     * encrypt the secret payload, in the following format:
     * `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;&#47;versions/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_version_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    private $kms_key_version_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $kms_key_version_name
     *           Required. The resource name of the Cloud KMS CryptoKeyVersion used to
     *           encrypt the secret payload, in the following format:
     *           `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;&#47;versions/&#42;`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Secretmanager\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The resource name of the Cloud KMS CryptoKeyVersion used to
     * encrypt the secret payload, in the following format:
     * `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;&#47;versions/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_version_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return string
     */
    public function getKmsKeyVersionName()
    {
        return $this->kms_key_version_name;
    }

    /**
     * Required. The resource name of the Cloud KMS CryptoKeyVersion used to
     * encrypt the secret payload, in the following format:
     * `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;&#47;versions/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_version_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param string $var
     * @return $this
     */
    public function setKmsKeyVersionName($var)
    {
        GPBUtil::checkString($var, True);
        $this->kms_key_version_name = $var;

        return $this;
    }

}

