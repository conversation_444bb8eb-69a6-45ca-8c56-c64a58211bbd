<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/secretmanager/v1/resources.proto

namespace Google\Cloud\SecretManager\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Configuration for encrypting secret payloads using customer-managed
 * encryption keys (CMEK).
 *
 * Generated from protobuf message <code>google.cloud.secretmanager.v1.CustomerManagedEncryption</code>
 */
class CustomerManagedEncryption extends \Google\Protobuf\Internal\Message
{
    /**
     * Required. The resource name of the Cloud KMS CryptoKey used to encrypt
     * secret payloads.
     * For secrets using the
     * [UserManaged][google.cloud.secretmanager.v1.Replication.UserManaged]
     * replication policy type, Cloud KMS CryptoKeys must reside in the same
     * location as the [replica location][Secret.UserManaged.Replica.location].
     * For secrets using the
     * [Automatic][google.cloud.secretmanager.v1.Replication.Automatic]
     * replication policy type, Cloud KMS CryptoKeys must reside in `global`.
     * The expected format is `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     */
    private $kms_key_name = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $kms_key_name
     *           Required. The resource name of the Cloud KMS CryptoKey used to encrypt
     *           secret payloads.
     *           For secrets using the
     *           [UserManaged][google.cloud.secretmanager.v1.Replication.UserManaged]
     *           replication policy type, Cloud KMS CryptoKeys must reside in the same
     *           location as the [replica location][Secret.UserManaged.Replica.location].
     *           For secrets using the
     *           [Automatic][google.cloud.secretmanager.v1.Replication.Automatic]
     *           replication policy type, Cloud KMS CryptoKeys must reside in `global`.
     *           The expected format is `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;`.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Secretmanager\V1\Resources::initOnce();
        parent::__construct($data);
    }

    /**
     * Required. The resource name of the Cloud KMS CryptoKey used to encrypt
     * secret payloads.
     * For secrets using the
     * [UserManaged][google.cloud.secretmanager.v1.Replication.UserManaged]
     * replication policy type, Cloud KMS CryptoKeys must reside in the same
     * location as the [replica location][Secret.UserManaged.Replica.location].
     * For secrets using the
     * [Automatic][google.cloud.secretmanager.v1.Replication.Automatic]
     * replication policy type, Cloud KMS CryptoKeys must reside in `global`.
     * The expected format is `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @return string
     */
    public function getKmsKeyName()
    {
        return $this->kms_key_name;
    }

    /**
     * Required. The resource name of the Cloud KMS CryptoKey used to encrypt
     * secret payloads.
     * For secrets using the
     * [UserManaged][google.cloud.secretmanager.v1.Replication.UserManaged]
     * replication policy type, Cloud KMS CryptoKeys must reside in the same
     * location as the [replica location][Secret.UserManaged.Replica.location].
     * For secrets using the
     * [Automatic][google.cloud.secretmanager.v1.Replication.Automatic]
     * replication policy type, Cloud KMS CryptoKeys must reside in `global`.
     * The expected format is `projects/&#42;&#47;locations/&#42;&#47;keyRings/&#42;&#47;cryptoKeys/&#42;`.
     *
     * Generated from protobuf field <code>string kms_key_name = 1 [(.google.api.field_behavior) = REQUIRED];</code>
     * @param string $var
     * @return $this
     */
    public function setKmsKeyName($var)
    {
        GPBUtil::checkString($var, True);
        $this->kms_key_name = $var;

        return $this;
    }

}

